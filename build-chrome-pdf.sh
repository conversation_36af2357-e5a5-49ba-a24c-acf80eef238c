#!/bin/bash

# Chrome PDF Service 构建和运行脚本

set -e

echo "🚀 Chrome PDF Service Builder"
echo "============================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ Java not found. Please install Java 11 or higher."
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 11 ]; then
    echo "❌ Java 11 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

echo "✅ Java version: $JAVA_VERSION"

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven not found. Please install Maven."
    exit 1
fi

echo "✅ <PERSON><PERSON> found"

# 检查Chrome
CHROME_FOUND=false
CHROME_PATHS=(
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    "/usr/bin/google-chrome"
    "/usr/bin/google-chrome-stable"
    "/usr/bin/chromium"
)

for chrome_path in "${CHROME_PATHS[@]}"; do
    if [ -f "$chrome_path" ] && [ -x "$chrome_path" ]; then
        echo "✅ Chrome found: $chrome_path"
        CHROME_FOUND=true
        break
    fi
done

if [ "$CHROME_FOUND" = false ]; then
    echo "⚠️  Chrome not found in standard locations"
    echo "   Please ensure Google Chrome is installed"
fi

# 创建PDF输出目录
PDF_DIR="/tmp/pdf"
if [ ! -d "$PDF_DIR" ]; then
    mkdir -p "$PDF_DIR"
    echo "✅ Created PDF output directory: $PDF_DIR"
else
    echo "✅ PDF output directory exists: $PDF_DIR"
fi

# 进入项目目录
cd pdf-application

echo ""
echo "🔨 Building Chrome PDF Service..."
echo "================================="

# 清理和编译
mvn clean compile -q

echo "✅ Compilation completed"

# 运行测试
echo ""
echo "🧪 Running Tests..."
echo "=================="

mvn test -Dtest=ChromePdfServiceTest -q

echo "✅ Tests completed"

# 打包
echo ""
echo "📦 Packaging..."
echo "==============="

mvn package -DskipTests -q

echo "✅ Packaging completed"

# 检查JAR文件
JAR_FILE="target/pdf-application-*.jar"
if ls $JAR_FILE 1> /dev/null 2>&1; then
    JAR_PATH=$(ls $JAR_FILE | head -n 1)
    echo "✅ JAR file created: $JAR_PATH"
else
    echo "❌ JAR file not found"
    exit 1
fi

echo ""
echo "🎉 Build completed successfully!"
echo "==============================="

echo ""
echo "📋 Usage Examples:"
echo "=================="

echo ""
echo "1. 启动Spring Boot服务:"
echo "   java -jar $JAR_PATH"
echo ""

echo "2. 命令行模式 - 单个URL:"
echo "   java -cp $JAR_PATH io.nue.pdf.cli.ChromePdfCli https://www.google.com"
echo ""

echo "3. 命令行模式 - 批量URL:"
echo "   java -cp $JAR_PATH io.nue.pdf.cli.ChromePdfCli https://www.google.com https://www.example.com"
echo ""

echo "4. 交互式模式:"
echo "   java -cp $JAR_PATH io.nue.pdf.cli.ChromePdfCli"
echo ""

echo "5. REST API测试:"
echo "   # 同步转换"
echo "   curl -X POST http://localhost:8080/api/chrome-pdf/convert-sync \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"url\":\"https://www.google.com\"}' \\"
echo "        --output google.pdf"
echo ""
echo "   # 异步转换"
echo "   curl -X POST http://localhost:8080/api/chrome-pdf/convert-async \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"url\":\"https://www.google.com\"}'"
echo ""
echo "   # 服务状态"
echo "   curl http://localhost:8080/api/chrome-pdf/status"
echo ""

echo "📁 PDF输出目录: $PDF_DIR"
echo "🔍 Magiclink选择器: div#magiclink-downloadpdf-hidden"
echo "⏱️  等待时间: 15秒"
echo "🔄 超时时间: 30秒"

echo ""
echo "💡 提示:"
echo "======="
echo "- 确保目标网站可以访问"
echo "- magiclink元素会在页面加载后自动检测"
echo "- 支持多线程并发处理"
echo "- 所有操作都有详细的日志记录"
echo "- PDF文件保存在 $PDF_DIR 目录"

# 询问是否立即测试
echo ""
read -p "是否立即测试Chrome PDF转换? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "🧪 Testing Chrome PDF conversion..."
    echo "=================================="
    
    java -cp "$JAR_PATH" io.nue.pdf.cli.ChromePdfCli https://www.example.com
    
    echo ""
    echo "📁 检查生成的PDF文件:"
    ls -la "$PDF_DIR"/*.pdf 2>/dev/null || echo "没有找到PDF文件"
fi

echo ""
echo "🎯 Chrome PDF Service 构建完成!"
