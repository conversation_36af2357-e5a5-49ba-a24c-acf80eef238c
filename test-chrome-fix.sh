#!/bin/bash

# 测试Chrome Multiple targets错误修复

set -e

echo "🔧 Chrome Multiple Targets Error Fix Test"
echo "========================================"

# 检查Chrome
CHROME_PATHS=(
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    "/usr/bin/google-chrome"
    "/usr/bin/google-chrome-stable"
    "/usr/bin/chromium"
)

CHROME_FOUND=""
for chrome_path in "${CHROME_PATHS[@]}"; do
    if [ -f "$chrome_path" ] && [ -x "$chrome_path" ]; then
        CHROME_FOUND="$chrome_path"
        echo "✅ Chrome found: $chrome_path"
        break
    fi
done

if [ -z "$CHROME_FOUND" ]; then
    echo "❌ Chrome not found"
    exit 1
fi

# 创建测试目录
TEST_DIR="/tmp/chrome-test"
mkdir -p "$TEST_DIR"

echo ""
echo "🧪 Test 1: 原始配置（可能出现Multiple targets错误）"
echo "================================================="

ORIGINAL_ARGS=(
    "--headless"
    "--disable-gpu"
    "--no-sandbox"
    "--print-to-pdf=$TEST_DIR/original.pdf"
    "--print-to-pdf-no-background"
    "--print-to-pdf-no-header"
    "--print-to-pdf-landscape"
    "--print-to-pdf-background"
    "--virtual-time-budget=20000"
    "--run-all-compositor-stages-before-draw"
    "--disable-background-timer-throttling"
    "--disable-renderer-backgrounding"
    "--disable-backgrounding-occluded-windows"
    "--lang=en-US"
    "--disable-sync"
    "--disable-default-apps"
    "--metrics-recording-only"
    "--disable-features=AllocatorFix"
    "--disable-extensions"
)

echo "测试原始配置..."
echo "命令: $CHROME_FOUND ${ORIGINAL_ARGS[*]} https://www.example.com"

if timeout 30s "$CHROME_FOUND" "${ORIGINAL_ARGS[@]}" "https://www.example.com" 2>&1 | tee "$TEST_DIR/original.log"; then
    if [ -f "$TEST_DIR/original.pdf" ] && [ -s "$TEST_DIR/original.pdf" ]; then
        echo "✅ 原始配置成功生成PDF"
        ls -la "$TEST_DIR/original.pdf"
    else
        echo "❌ 原始配置PDF生成失败"
    fi
else
    echo "❌ 原始配置执行失败"
    echo "错误日志:"
    cat "$TEST_DIR/original.log" | grep -i "error\|multiple targets" || echo "无特定错误信息"
fi

echo ""
echo "🔧 Test 2: 修复后配置（应该解决Multiple targets错误）"
echo "=================================================="

FIXED_ARGS=(
    "--headless"
    "--single-process"           # 🔑 关键修复
    "--no-zygote"               # 🔑 关键修复
    "--disable-gpu"
    "--no-sandbox"
    "--disable-setuid-sandbox"
    "--print-to-pdf=$TEST_DIR/fixed.pdf"
    "--print-to-pdf-no-header"
    "--print-to-pdf-landscape"
    "--virtual-time-budget=20000"
    "--run-all-compositor-stages-before-draw"
    "--disable-background-timer-throttling"
    "--disable-renderer-backgrounding"
    "--disable-backgrounding-occluded-windows"
    "--disable-extensions"
    "--disable-plugins"
    "--memory-pressure-off"
    "--disable-background-networking"
    "--disable-web-security"
    "--ignore-certificate-errors"
    "--window-size=1920,1080"
    "--force-device-scale-factor=1"
)

echo "测试修复后配置..."
echo "命令: $CHROME_FOUND ${FIXED_ARGS[*]} https://www.example.com"

if timeout 30s "$CHROME_FOUND" "${FIXED_ARGS[@]}" "https://www.example.com" 2>&1 | tee "$TEST_DIR/fixed.log"; then
    if [ -f "$TEST_DIR/fixed.pdf" ] && [ -s "$TEST_DIR/fixed.pdf" ]; then
        echo "✅ 修复后配置成功生成PDF"
        ls -la "$TEST_DIR/fixed.pdf"
    else
        echo "❌ 修复后配置PDF生成失败"
    fi
else
    echo "❌ 修复后配置执行失败"
    echo "错误日志:"
    cat "$TEST_DIR/fixed.log" | grep -i "error\|multiple targets" || echo "无特定错误信息"
fi

echo ""
echo "🧪 Test 3: 最小化配置（最稳定）"
echo "============================="

MINIMAL_ARGS=(
    "--headless"
    "--single-process"
    "--no-zygote"
    "--disable-gpu"
    "--no-sandbox"
    "--print-to-pdf=$TEST_DIR/minimal.pdf"
    "--print-to-pdf-no-header"
    "--virtual-time-budget=15000"
)

echo "测试最小化配置..."
echo "命令: $CHROME_FOUND ${MINIMAL_ARGS[*]} https://www.example.com"

if timeout 30s "$CHROME_FOUND" "${MINIMAL_ARGS[@]}" "https://www.example.com" 2>&1 | tee "$TEST_DIR/minimal.log"; then
    if [ -f "$TEST_DIR/minimal.pdf" ] && [ -s "$TEST_DIR/minimal.pdf" ]; then
        echo "✅ 最小化配置成功生成PDF"
        ls -la "$TEST_DIR/minimal.pdf"
    else
        echo "❌ 最小化配置PDF生成失败"
    fi
else
    echo "❌ 最小化配置执行失败"
    echo "错误日志:"
    cat "$TEST_DIR/minimal.log" | grep -i "error\|multiple targets" || echo "无特定错误信息"
fi

echo ""
echo "📊 测试结果汇总"
echo "=============="

echo "生成的文件:"
ls -la "$TEST_DIR"/*.pdf 2>/dev/null || echo "没有PDF文件生成"

echo ""
echo "错误分析:"
echo "--------"

# 检查Multiple targets错误
if grep -q "Multiple targets are not supported" "$TEST_DIR"/*.log 2>/dev/null; then
    echo "❌ 发现 'Multiple targets are not supported' 错误"
    echo "   出现在以下日志中:"
    grep -l "Multiple targets are not supported" "$TEST_DIR"/*.log 2>/dev/null
else
    echo "✅ 未发现 'Multiple targets are not supported' 错误"
fi

# 检查其他常见错误
if grep -q "ERROR" "$TEST_DIR"/*.log 2>/dev/null; then
    echo "⚠️  发现其他错误:"
    grep "ERROR" "$TEST_DIR"/*.log 2>/dev/null | head -5
else
    echo "✅ 未发现其他严重错误"
fi

echo ""
echo "💡 建议配置"
echo "=========="
echo "基于测试结果，推荐使用以下Chrome参数配置:"
echo ""
echo "必需参数（修复Multiple targets错误）:"
echo "  --headless"
echo "  --single-process      # 🔑 关键"
echo "  --no-zygote          # 🔑 关键"
echo "  --disable-gpu"
echo "  --no-sandbox"
echo ""
echo "PDF输出参数:"
echo "  --print-to-pdf=<path>"
echo "  --print-to-pdf-no-header"
echo "  --virtual-time-budget=15000"
echo ""
echo "可选优化参数:"
echo "  --disable-extensions"
echo "  --disable-plugins"
echo "  --memory-pressure-off"
echo "  --disable-web-security"
echo "  --window-size=1920,1080"

echo ""
echo "🎯 测试完成！"
echo "测试文件保存在: $TEST_DIR"
