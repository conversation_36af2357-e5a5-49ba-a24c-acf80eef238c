import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Test Chrome command execution - converts your working command line to Java ProcessBuilder
 */
public class ChromeCommandTest {
    
    private static final String CHROME_BINARY = "/Users/<USER>/workspace-rc/nue-pdf-main/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium";
    private static final String TEST_URL = "https://app.test.nue.io/view-invoice/BOCNeFOeuvg3rz3HBhV-CkqdZAClRL5E88C73H0FMtjE5fiE7WM7r4j7W1RXX_e7CCLNduw0WvyJU0YBYKst49bIha2aO7pjRX0d7V5e1gGkrlsFYFqd0s2UarfEWLAZ7E-CQbvjh_Bg792yf1n813seAnadShlICFI6U9OEhOMBemIyyz70i94pMiJ_UfDVkZJlm6byvo=?internal=true";
    
    /**
     * Build Chrome command based on your working command line
     * This exactly matches your successful command line execution
     */
    private static List<String> buildChromeCommand() {
        List<String> command = new ArrayList<>();
        
        // Chrome executable path
        command.add(CHROME_BINARY);
        
        // Chrome arguments (exactly matching your working command)
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf=/Users/<USER>/Downloads/magiclink background2.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");
        
        // URL (last parameter)
        command.add(TEST_URL);
        
        return command;
    }
    
    public static void main(String[] args) {
        System.out.println("=== Chrome Command Test ===");
        System.out.println("Converting your working command line to Java ProcessBuilder");
        System.out.println();
        
        try {
            // Build command
            List<String> command = buildChromeCommand();
            
            System.out.println("Command built with " + command.size() + " arguments:");
            for (int i = 0; i < command.size(); i++) {
                String arg = command.get(i);
                if (arg.length() > 80) {
                    System.out.println("  [" + i + "] " + arg.substring(0, 80) + "...");
                } else {
                    System.out.println("  [" + i + "] " + arg);
                }
            }
            System.out.println();
            
            // Show the equivalent command line
            System.out.println("Equivalent command line:");
            System.out.println("\"" + command.get(0) + "\" \\");
            for (int i = 1; i < command.size() - 1; i++) {
                String arg = command.get(i);
                if (arg.contains(" ") || arg.contains("=")) {
                    System.out.println("    \"" + arg + "\" \\");
                } else {
                    System.out.println("    " + arg + " \\");
                }
            }
            System.out.println("    \"" + command.get(command.size() - 1) + "\"");
            System.out.println();
            
            // Create ProcessBuilder
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            
            System.out.println("Executing Chrome process...");
            Process process = processBuilder.start();
            
            // Read output
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    System.out.println("Chrome: " + line);
                }
            }
            
            // Wait for process completion
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                System.out.println("ERROR: Process timed out after 30 seconds");
                System.exit(1);
            }
            
            int exitCode = process.exitValue();
            System.out.println();
            System.out.println("Process finished with exit code: " + exitCode);
            
            // Check for Multiple targets error
            String outputStr = output.toString();
            if (outputStr.contains("Multiple targets are not supported")) {
                System.out.println("ERROR: Multiple targets error detected!");
                System.out.println("This indicates the ProcessBuilder conversion failed");
                System.exit(1);
            } else {
                System.out.println("SUCCESS: No Multiple targets error detected");
            }
            
            // Check if PDF was created
            java.io.File pdfFile = new java.io.File("/Users/<USER>/Downloads/magiclink background2.pdf");
            if (pdfFile.exists() && pdfFile.length() > 0) {
                System.out.println("SUCCESS: PDF file created - " + pdfFile.length() + " bytes");
                System.out.println("File location: " + pdfFile.getAbsolutePath());
            } else {
                System.out.println("WARNING: PDF file not created or empty");
            }
            
            // Final result
            if (exitCode == 0) {
                System.out.println();
                System.out.println("=== TEST PASSED ===");
                System.out.println("Your command line has been successfully converted to Java ProcessBuilder!");
                System.out.println("You can use the buildChromeCommand() method in your application.");
            } else {
                System.out.println();
                System.out.println("=== TEST FAILED ===");
                System.out.println("Exit code: " + exitCode);
                System.out.println("Output: " + outputStr);
                System.exit(1);
            }
            
        } catch (Exception e) {
            System.out.println("ERROR: Exception occurred during test");
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * Alternative method with different PDF filename to avoid conflicts
     */
    public static List<String> buildChromeCommandWithCustomPdf(String pdfPath) {
        List<String> command = new ArrayList<>();
        
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf=" + pdfPath);
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");
        command.add(TEST_URL);
        
        return command;
    }
    
    /**
     * Method that can be used in your application
     */
    public static List<String> buildChromeCommandForApplication(String chromeBinary, String pdfPath, String url) {
        List<String> command = new ArrayList<>();
        
        command.add(chromeBinary);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf=" + pdfPath);
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");
        command.add(url);
        
        return command;
    }
}
