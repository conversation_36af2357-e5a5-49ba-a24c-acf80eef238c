# Nue Pdf Generator

### Feature includes
Must set SPRING_PROFILES_ACTIVE=dev when running this application in dev environment.
### Build
```shell
mvn clean install
```
#### Sample rest apis
#### User Session and Logging filters
For local development, use spring profile as `dev` for logging into console.
For other env, the logs will be logged with JSON format.

#### API doc and client generator
To generate the openapi docs and client.
```shell
mvn -PapiDoc clean install
mvn -PapiDoc clean install -Dspring.profiles.active=dev -DskipTests
```

#### Docker
Build application docker image
```shell
cd application && docker build --build-arg BUILD_DIR=target --build-arg VERSION=0.0.1-SNAPSHOT --build-arg PORT=5300 . -f docker/Dockerfile -t nue-magiclink-to-pdf-app
```
This script is used to init local dev environment.
```shell
/bin/bash ./chromium-dev-init.sh
```
Docker image is built ref to:
https://github.com/SeleniumHQ/docker-selenium/tree/trunk/NodeChromium

----------------
Downlaod by webkit use wkhtmltopdf

# For M2 apple silion chips
docker pull --platform=linux/arm64 sofiaadmin/python-wkhtmltopdf-docker-primary:0.0.3

# build image
docker build \
-t html2pdf-arm \
--build-arg PORT="5300" \
-f docker/App.Wkhtmltopdf.Dockerfile .

# Run docker container
docker run --rm -p 5300:5300 -v $(pwd)/pdfs:/output html2pdf-arm

# convert from html to pdf
curl -X POST -H "Content-Type: text/html" -d @sample.html http://localhost:5300/html-to-pdf

home:
https://wkhtmltopdf.org/
https://wkhtmltopdf.org/libwkhtmltox/
https://wkhtmltopdf.org/usage/wkhtmltopdf.txt
