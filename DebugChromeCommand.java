import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 调试Chrome命令执行问题
 */
public class DebugChromeCommand {
    
    private static final String CHROME_BINARY = "/Users/<USER>/workspace-rc/nue-pdf-main/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium";
    private static final String TEST_URL = "https://app.test.nue.io/view-invoice/BOCNeFOeuvg3rz3HBhV-CkqdZAClRL5E88C73H0FMtjE5fiE7WM7r4j7W1RXX_e7CCLNduw0WvyJU0YBYKst49bIha2aO7pjRX0d7V5e1gGkrlsFYFqd0s2UarfEWLAZ7E-CQbvjh_Bg792yf1n813seAnadShlICFI6U9OEhOMBemIyyz70i94pMiJ_UfDVkZJlm6byvo=?internal=true";
    private static final String OUTPUT_DIR = "/tmp/chrome-debug";
    
    public static void main(String[] args) {
        System.out.println("🔧 Debug Chrome Command Execution");
        System.out.println("==================================");
        
        // 创建输出目录
        new java.io.File(OUTPUT_DIR).mkdirs();
        
        // 测试1: 模拟Java应用的原始命令（有问题的）
        System.out.println("\n❌ Test 1: Java应用原始命令（模拟失败的情况）");
        testJavaOriginalCommand();
        
        // 测试2: 修复后的命令（添加--single-process --no-zygote）
        System.out.println("\n🔧 Test 2: 修复后的命令");
        testFixedCommand();
        
        // 测试3: 最小化命令
        System.out.println("\n✨ Test 3: 最小化命令");
        testMinimalCommand();
        
        // 测试4: 使用--headless=new参数
        System.out.println("\n🆕 Test 4: 使用新的headless模式");
        testNewHeadlessCommand();
        
        System.out.println("\n🎯 Debug completed!");
    }
    
    private static void testJavaOriginalCommand() {
        // 模拟Java应用中的原始命令
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf");
        command.add(OUTPUT_DIR + "/java-original.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget");
        command.add("20000");
        command.add(TEST_URL);
        
        executeCommand("Java Original", command);
    }
    
    private static void testFixedCommand() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--single-process");        // 🔑 关键修复
        command.add("--no-zygote");            // 🔑 关键修复
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf");
        command.add(OUTPUT_DIR + "/fixed.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget");
        command.add("20000");
        command.add(TEST_URL);
        
        executeCommand("Fixed", command);
    }
    
    private static void testMinimalCommand() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--single-process");
        command.add("--no-zygote");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--print-to-pdf");
        command.add(OUTPUT_DIR + "/minimal.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--virtual-time-budget");
        command.add("15000");
        command.add(TEST_URL);
        
        executeCommand("Minimal", command);
    }
    
    private static void testNewHeadlessCommand() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless=new");          // 🆕 新的headless模式
        command.add("--single-process");
        command.add("--no-zygote");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--print-to-pdf");
        command.add(OUTPUT_DIR + "/new-headless.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--virtual-time-budget");
        command.add("15000");
        command.add(TEST_URL);
        
        executeCommand("New Headless", command);
    }
    
    private static void executeCommand(String testName, List<String> command) {
        System.out.println("执行 " + testName + " 测试...");
        System.out.println("命令参数数量: " + command.size());
        System.out.println("Chrome二进制: " + command.get(0));
        System.out.println("URL: " + command.get(command.size() - 1));
        
        // 打印关键参数
        System.out.print("关键参数: ");
        for (String arg : command) {
            if (arg.equals("--headless") || arg.equals("--headless=new") || 
                arg.equals("--single-process") || arg.equals("--no-zygote")) {
                System.out.print(arg + " ");
            }
        }
        System.out.println();
        
        try {
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    System.out.println("  " + line);
                }
            }
            
            // 等待进程完成
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            int exitCode = finished ? process.exitValue() : -1;
            
            if (!finished) {
                process.destroyForcibly();
                System.out.println("❌ " + testName + " 测试超时");
            } else if (exitCode == 0) {
                System.out.println("✅ " + testName + " 测试成功 (退出码: " + exitCode + ")");
                
                // 检查PDF文件
                String pdfFile = null;
                for (String arg : command) {
                    if (arg.endsWith(".pdf")) {
                        pdfFile = arg;
                        break;
                    }
                }
                
                if (pdfFile != null) {
                    java.io.File pdf = new java.io.File(pdfFile);
                    if (pdf.exists() && pdf.length() > 0) {
                        System.out.println("✅ PDF文件生成: " + pdfFile + " (" + pdf.length() + " 字节)");
                    } else {
                        System.out.println("❌ PDF文件未生成或为空");
                    }
                }
            } else {
                System.out.println("❌ " + testName + " 测试失败 (退出码: " + exitCode + ")");
            }
            
            // 检查Multiple targets错误
            String outputStr = output.toString();
            if (outputStr.contains("Multiple targets are not supported")) {
                System.out.println("❌ 检测到Multiple targets错误");
            } else {
                System.out.println("✅ 无Multiple targets错误");
            }
            
        } catch (Exception e) {
            System.out.println("❌ " + testName + " 测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
