import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 测试Multiple targets错误修复
 */
public class TestMultipleTargetsFix {
    
    private static final String CHROME_BINARY = "/Users/<USER>/workspace-rc/nue-pdf-main/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium";
    private static final String TEST_URL = "https://www.example.com";
    private static final String OUTPUT_DIR = "/tmp/chrome-test";
    
    public static void main(String[] args) {
        System.out.println("🔧 Testing Multiple Targets Error Fix");
        System.out.println("=====================================");
        
        // 创建输出目录
        new java.io.File(OUTPUT_DIR).mkdirs();
        
        // 测试1: 原始有问题的配置
        System.out.println("\n🧪 Test 1: Original problematic configuration");
        testOriginalConfig();
        
        // 测试2: 修复后的配置
        System.out.println("\n🔧 Test 2: Fixed configuration");
        testFixedConfig();
        
        // 测试3: 最小化配置
        System.out.println("\n✨ Test 3: Minimal configuration");
        testMinimalConfig();
        
        System.out.println("\n🎯 Test completed!");
    }
    
    private static void testOriginalConfig() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--print-to-pdf=" + OUTPUT_DIR + "/original.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");
        command.add("--run-all-compositor-stages-before-draw");
        command.add("--disable-background-timer-throttling");
        command.add("--disable-renderer-backgrounding");
        command.add("--disable-backgrounding-occluded-windows");
        command.add("--disable-extensions");
        command.add("--disable-plugins");
        command.add("--memory-pressure-off");
        command.add("--disable-background-networking");
        command.add(TEST_URL);
        
        executeCommand("Original", command);
    }
    
    private static void testFixedConfig() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        
        // 🔑 关键修复参数（必须在最前面）
        command.add("--headless");
        command.add("--single-process");        // 🔑 强制单进程
        command.add("--no-zygote");            // 🔑 禁用zygote
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        
        // 进程和渲染控制
        command.add("--disable-dev-shm-usage");
        command.add("--disable-extensions");
        command.add("--disable-plugins");
        command.add("--disable-background-networking");
        command.add("--disable-background-timer-throttling");
        command.add("--disable-renderer-backgrounding");
        command.add("--disable-backgrounding-occluded-windows");
        
        // 内存和性能优化
        command.add("--memory-pressure-off");
        command.add("--max_old_space_size=4096");
        command.add("--disable-web-security");
        command.add("--ignore-certificate-errors");
        command.add("--ignore-ssl-errors");
        
        // PDF输出配置
        command.add("--print-to-pdf=" + OUTPUT_DIR + "/fixed.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        
        // 等待时间配置
        command.add("--virtual-time-budget=20000");
        command.add("--run-all-compositor-stages-before-draw");
        
        // 窗口设置
        command.add("--window-size=1920,1080");
        command.add("--force-device-scale-factor=1");
        
        // URL
        command.add(TEST_URL);
        
        executeCommand("Fixed", command);
    }
    
    private static void testMinimalConfig() {
        List<String> command = new ArrayList<>();
        command.add(CHROME_BINARY);
        command.add("--headless");
        command.add("--single-process");
        command.add("--no-zygote");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--print-to-pdf=" + OUTPUT_DIR + "/minimal.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--virtual-time-budget=15000");
        command.add(TEST_URL);
        
        executeCommand("Minimal", command);
    }
    
    private static void executeCommand(String testName, List<String> command) {
        System.out.println("Executing " + testName + " test...");
        System.out.println("Command: " + String.join(" ", command));
        
        try {
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    System.out.println("  " + line);
                }
            }
            
            // 等待进程完成
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            int exitCode = finished ? process.exitValue() : -1;
            
            if (!finished) {
                process.destroyForcibly();
                System.out.println("❌ " + testName + " test timed out");
            } else if (exitCode == 0) {
                System.out.println("✅ " + testName + " test completed successfully (exit code: " + exitCode + ")");
                
                // 检查PDF文件
                String pdfFile = OUTPUT_DIR + "/" + testName.toLowerCase() + ".pdf";
                java.io.File pdf = new java.io.File(pdfFile);
                if (pdf.exists() && pdf.length() > 0) {
                    System.out.println("✅ PDF file generated: " + pdfFile + " (" + pdf.length() + " bytes)");
                } else {
                    System.out.println("❌ PDF file not generated or empty");
                }
            } else {
                System.out.println("❌ " + testName + " test failed (exit code: " + exitCode + ")");
            }
            
            // 检查Multiple targets错误
            String outputStr = output.toString();
            if (outputStr.contains("Multiple targets are not supported")) {
                System.out.println("❌ Multiple targets error detected in " + testName + " test");
            } else {
                System.out.println("✅ No Multiple targets error in " + testName + " test");
            }
            
        } catch (Exception e) {
            System.out.println("❌ " + testName + " test failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
