package io.nue.pdf.utils;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for converting relative paths to absolute paths in HTML content
 */
public class HtmlPathResolver {
    
    private static final Pattern CSS_URL_PATTERN = Pattern.compile("url\\(['\"]?([^'\"\\)]+)['\"]?\\)");
    
    /**
     * Convert all relative paths in HTML to absolute paths
     * 
     * @param html The HTML content
     * @param baseUrl The base URL to resolve relative paths against
     * @return HTML with absolute paths
     */
    public static String convertToAbsolutePaths(String html, String baseUrl) {
        if (html == null || html.trim().isEmpty()) {
            return html;
        }
        
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            return html;
        }
        
        try {
            Document doc = Jsoup.parse(html, baseUrl);
            doc.setBaseUri(baseUrl);
            
            // Convert src attributes (images, scripts, iframes, etc.)
            convertSrcAttributes(doc);
            
            // Convert href attributes (links, stylesheets)
            convertHrefAttributes(doc);
            
            // Convert CSS background images and other URL references in style attributes
            convertStyleAttributes(doc, baseUrl);
            
            return doc.outerHtml();
            
        } catch (Exception e) {
            // If parsing fails, return original HTML
            System.err.println("Failed to parse HTML for path resolution: " + e.getMessage());
            return html;
        }
    }
    
    /**
     * Convert src attributes to absolute URLs
     */
    private static void convertSrcAttributes(Document doc) {
        Elements elementsWithSrc = doc.select("[src]");
        for (Element element : elementsWithSrc) {
            String relativeSrc = element.attr("src");
            String absoluteSrc = element.absUrl("src");
            
            if (!absoluteSrc.isEmpty() && !relativeSrc.equals(absoluteSrc)) {
                element.attr("src", absoluteSrc);
            }
        }
    }
    
    /**
     * Convert href attributes to absolute URLs
     */
    private static void convertHrefAttributes(Document doc) {
        Elements elementsWithHref = doc.select("[href]");
        for (Element element : elementsWithHref) {
            String relativeHref = element.attr("href");
            String absoluteHref = element.absUrl("href");
            
            if (!absoluteHref.isEmpty() && !relativeHref.equals(absoluteHref)) {
                element.attr("href", absoluteHref);
            }
        }
    }
    
    /**
     * Convert CSS URLs in style attributes to absolute URLs
     */
    private static void convertStyleAttributes(Document doc, String baseUrl) {
        Elements elementsWithStyle = doc.select("[style]");
        for (Element element : elementsWithStyle) {
            String style = element.attr("style");
            String updatedStyle = convertCssUrls(style, baseUrl);
            
            if (!style.equals(updatedStyle)) {
                element.attr("style", updatedStyle);
            }
        }
    }
    
    /**
     * Convert relative URLs in CSS to absolute URLs
     */
    private static String convertCssUrls(String css, String baseUrl) {
        if (css == null || css.trim().isEmpty()) {
            return css;
        }
        
        Matcher matcher = CSS_URL_PATTERN.matcher(css);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String url = matcher.group(1);
            String absoluteUrl = resolveUrl(url, baseUrl);
            matcher.appendReplacement(result, "url('" + absoluteUrl + "')");
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * Resolve a relative URL against a base URL
     */
    private static String resolveUrl(String url, String baseUrl) {
        try {
            URI base = new URI(baseUrl);
            URI resolved = base.resolve(url);
            return resolved.toString();
        } catch (URISyntaxException e) {
            // If resolution fails, return original URL
            return url;
        }
    }
    
    /**
     * Convert file path to file:// URL
     */
    public static String filePathToUrl(String filePath) {
        if (filePath == null) {
            return null;
        }
        
        // Handle different path formats
        if (filePath.startsWith("file://")) {
            return filePath;
        }
        
        if (filePath.startsWith("/")) {
            return "file://" + filePath;
        }
        
        // Relative path - convert to absolute first
        try {
            java.io.File file = new java.io.File(filePath);
            return file.toURI().toString();
        } catch (Exception e) {
            return "file://" + filePath;
        }
    }
    
    /**
     * Get base URL for test resources
     */
    public static String getTestResourceBaseUrl() {
        try {
            java.io.File resourcesDir = new java.io.File("src/test/resources");
            return resourcesDir.toURI().toString();
        } catch (Exception e) {
            return "file://src/test/resources/";
        }
    }
}
