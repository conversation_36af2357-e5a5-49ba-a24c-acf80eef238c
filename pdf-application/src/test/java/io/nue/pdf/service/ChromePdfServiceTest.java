package io.nue.pdf.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Chrome PDF Service 测试类
 */
class ChromePdfServiceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ChromePdfServiceTest.class);
    
    private ChromePdfService chromePdfService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        chromePdfService = new ChromePdfService();
    }
    
    @Test
    void testConvertGoogleToPdf() throws Exception {
        logger.info("Testing Google homepage conversion to PDF");
        
        String url = "https://www.google.com";
        String pdfFilePath = chromePdfService.convertUrlToPdf(url);
        
        assertNotNull(pdfFilePath, "PDF file path should not be null");
        
        File pdfFile = new File(pdfFilePath);
        assertTrue(pdfFile.exists(), "PDF file should exist");
        assertTrue(pdfFile.length() > 0, "PDF file should not be empty");
        
        logger.info("PDF generated successfully: {} (size: {} bytes)", pdfFilePath, pdfFile.length());
    }
    
    @Test
    void testAsyncConversion() throws Exception {
        logger.info("Testing async PDF conversion");
        
        String url = "https://www.example.com";
        CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
        
        assertNotNull(future, "Future should not be null");
        
        // 等待完成，最多30秒
        String pdfFilePath = future.get(30, TimeUnit.SECONDS);
        
        assertNotNull(pdfFilePath, "PDF file path should not be null");
        
        File pdfFile = new File(pdfFilePath);
        assertTrue(pdfFile.exists(), "PDF file should exist");
        assertTrue(pdfFile.length() > 0, "PDF file should not be empty");
        
        logger.info("Async PDF generated successfully: {} (size: {} bytes)", pdfFilePath, pdfFile.length());
    }
    
    @Test
    void testMultiThreadedConversion() throws Exception {
        logger.info("Testing multi-threaded PDF conversion");
        
        List<String> urls = List.of(
            "https://www.google.com",
            "https://www.example.com",
            "https://httpbin.org/html"
        );
        
        List<CompletableFuture<String>> futures = new ArrayList<>();
        
        // 启动多个异步任务
        for (String url : urls) {
            CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
            futures.add(future);
        }
        
        // 等待所有任务完成
        List<String> pdfFilePaths = new ArrayList<>();
        for (CompletableFuture<String> future : futures) {
            String pdfFilePath = future.get(45, TimeUnit.SECONDS);
            pdfFilePaths.add(pdfFilePath);
        }
        
        assertEquals(urls.size(), pdfFilePaths.size(), "Should generate PDF for each URL");
        
        // 验证所有PDF文件
        for (String pdfFilePath : pdfFilePaths) {
            assertNotNull(pdfFilePath, "PDF file path should not be null");
            
            File pdfFile = new File(pdfFilePath);
            assertTrue(pdfFile.exists(), "PDF file should exist: " + pdfFilePath);
            assertTrue(pdfFile.length() > 0, "PDF file should not be empty: " + pdfFilePath);
            
            logger.info("Multi-threaded PDF generated: {} (size: {} bytes)", pdfFilePath, pdfFile.length());
        }
    }
    
    @Test
    void testInvalidUrl() {
        logger.info("Testing invalid URL handling");
        
        String invalidUrl = "invalid-url-format";
        
        assertThrows(Exception.class, () -> {
            chromePdfService.convertUrlToPdf(invalidUrl);
        }, "Should throw exception for invalid URL");
    }
    
    @Test
    void testThreadPoolStatus() {
        logger.info("Testing thread pool status");
        
        String status = chromePdfService.getThreadPoolStatus();
        
        assertNotNull(status, "Thread pool status should not be null");
        assertTrue(status.contains("ThreadPool Status"), "Status should contain thread pool information");
        
        logger.info("Thread pool status: {}", status);
    }
    
    @Test
    void testMagiclinkSimulation() throws Exception {
        logger.info("Testing magiclink simulation with httpbin");
        
        // 使用httpbin来模拟包含特定元素的页面
        String url = "https://httpbin.org/html";
        
        CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
        String pdfFilePath = future.get(30, TimeUnit.SECONDS);
        
        assertNotNull(pdfFilePath, "PDF file path should not be null");
        
        File pdfFile = new File(pdfFilePath);
        assertTrue(pdfFile.exists(), "PDF file should exist");
        assertTrue(pdfFile.length() > 0, "PDF file should not be empty");
        
        logger.info("Magiclink simulation PDF generated: {} (size: {} bytes)", pdfFilePath, pdfFile.length());
    }
    
    @Test
    void testConcurrentRequests() throws Exception {
        logger.info("Testing concurrent requests handling");
        
        int numberOfRequests = 5;
        String url = "https://www.example.com";
        
        List<CompletableFuture<String>> futures = new ArrayList<>();
        
        // 同时启动多个请求
        for (int i = 0; i < numberOfRequests; i++) {
            CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
            futures.add(future);
        }
        
        // 等待所有请求完成
        int successCount = 0;
        for (CompletableFuture<String> future : futures) {
            try {
                String pdfFilePath = future.get(60, TimeUnit.SECONDS);
                if (pdfFilePath != null) {
                    File pdfFile = new File(pdfFilePath);
                    if (pdfFile.exists() && pdfFile.length() > 0) {
                        successCount++;
                        logger.info("Concurrent request PDF generated: {} (size: {} bytes)", 
                                   pdfFilePath, pdfFile.length());
                    }
                }
            } catch (Exception e) {
                logger.warn("Concurrent request failed", e);
            }
        }
        
        assertTrue(successCount > 0, "At least one concurrent request should succeed");
        logger.info("Concurrent requests completed: {}/{} successful", successCount, numberOfRequests);
    }
}
