package io.nue.pdf.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.config.ChromeOptionsConfig;
import io.nue.util.TestSourceLoader;
import org.assertj.core.util.Lists;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Unit tests for PDF generation service Tests HTML to PDF conversion using wkhtmltopdf with the same parameters as buildWkhtmltopdfCommand
 */
class PdfGenerationServiceTest {

    private static final Logger log = LoggerFactory.getLogger(PdfGenerationServiceTest.class);

    private ThreadPoolExecutor threadPoolExecutor;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // Create thread pool for testing
        threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(4);
    }
    
    @Test
    public void hh() throws IOException, InterruptedException {
        ChromeOptionsConfig config = new ChromeOptionsConfig();
        ChromeOptions chromeOptions = config.chromiumOptions();
        List<String> commands = chromeOptions.toCommand();

        // execute Chrome command
        ProcessBuilder processBuilder = new ProcessBuilder(commands);
        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();

        // read output log
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("Chrome output: {}", line);
            }
        }

        // wait for process finished
        boolean finished = process.waitFor(30000, TimeUnit.MILLISECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("Chrome process timeout after " + 30000 + "ms");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            log.error("Task-{}: Chrome process failed with output: {}", 1, output.toString());
        } else {
            log.info("Task-{}: Chrome process finished with exit code: {}", 1, exitCode);
        }
    }
}
