package io.nue.pdf.service;

import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.config.ChromeOptionsConfig;
import org.assertj.core.util.Lists;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.nue.util.TestSourceLoader;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PDF generation service Tests HTML to PDF conversion using wkhtmltopdf with the same parameters as buildWkhtmltopdfCommand
 */
class PdfGenerationServiceTest {

    private static final Logger log = LoggerFactory.getLogger(PdfGenerationServiceTest.class);

    private ThreadPoolExecutor threadPoolExecutor;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // Create thread pool for testing
        threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(4);
    }

    @Test
    void testHtmlToPdfWithSameParametersAsBuildCommand() throws Exception {
        // Test HTML content
        String htmlContent = createTestHtmlContent();

        // Generate PDF using direct wkhtmltopdf command with same parameters
        String pdfFileName = generatePdfWithSameParameters(htmlContent);

        // Verify PDF was created
        Path pdfPath = tempDir.resolve(pdfFileName);
        assertTrue(Files.exists(pdfPath), "PDF file should be created");
        assertTrue(Files.size(pdfPath) > 0, "PDF file should not be empty");

        // Verify PDF content (basic check)
        byte[] pdfContent = Files.readAllBytes(pdfPath);
        assertTrue(pdfContent.length > 1000, "PDF should have reasonable size");

        // Check PDF header
        String pdfHeader = new String(pdfContent, 0, Math.min(10, pdfContent.length));
        assertTrue(pdfHeader.startsWith("%PDF-"), "File should be a valid PDF");
    }

    @Test
    void testAsyncHtmlToPdf() throws Exception {
        String htmlContent = createTestHtmlContent();

        // Test async PDF generation
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            try {
                return generatePdfWithSameParameters(htmlContent);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, threadPoolExecutor);

        String pdfFileName = future.get(30, TimeUnit.SECONDS);
        assertNotNull(pdfFileName, "PDF filename should not be null");

        Path pdfPath = tempDir.resolve(pdfFileName);
        assertTrue(Files.exists(pdfPath), "Async generated PDF should exist");
    }

    @Test
    void testMultipleHtmlToPdfConcurrent() throws Exception {
        String htmlContent = createTestHtmlContent();
        int concurrentRequests = 3;

        List<CompletableFuture<String>> futures = new ArrayList<>();

        // Start multiple concurrent PDF generations
        for (int i = 0; i < concurrentRequests; i++) {
            final int requestId = i;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    String modifiedHtml = htmlContent.replace("Test PDF Document",
                            "Test PDF Document #" + requestId);
                    return generatePdfWithSameParameters(modifiedHtml);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, threadPoolExecutor);
            futures.add(future);
        }

        // Wait for all to complete
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        allFutures.get(60, TimeUnit.SECONDS);

        // Verify all PDFs were created
        for (CompletableFuture<String> future : futures) {
            String pdfFileName = future.get();
            assertNotNull(pdfFileName, "PDF filename should not be null");

            Path pdfPath = tempDir.resolve(pdfFileName);
            assertTrue(Files.exists(pdfPath), "Concurrent generated PDF should exist");
            assertTrue(Files.size(pdfPath) > 0, "Concurrent generated PDF should not be empty");
        }
    }

    @Test
    void testWkhtmltopdfParametersMatch() {
        // Test that our command parameters match the buildWkhtmltopdfCommand method
        String outputPath = tempDir.resolve("test.pdf").toString();
        List<String> expectedCommand = buildWkhtmltopdfCommandForTest(outputPath);

        // Verify all expected parameters are present
        assertTrue(expectedCommand.contains("wkhtmltopdf"), "Should contain wkhtmltopdf command");
        assertTrue(expectedCommand.contains("--page-size"), "Should contain page-size parameter");
        assertTrue(expectedCommand.contains("A4"), "Should contain A4 page size");
        assertTrue(expectedCommand.contains("--margin-top"), "Should contain margin-top parameter");
        assertTrue(expectedCommand.contains("0.75in"), "Should contain 0.75in margin");
        assertTrue(expectedCommand.contains("--encoding"), "Should contain encoding parameter");
        assertTrue(expectedCommand.contains("UTF-8"), "Should contain UTF-8 encoding");
        assertTrue(expectedCommand.contains("--no-outline"), "Should contain no-outline parameter");
        assertTrue(expectedCommand.contains("--disable-smart-shrinking"), "Should contain disable-smart-shrinking");
        assertTrue(expectedCommand.contains("--print-media-type"), "Should contain print-media-type");
        assertTrue(expectedCommand.contains("--load-error-handling"), "Should contain load-error-handling");
        assertTrue(expectedCommand.contains("ignore"), "Should contain ignore parameter");
        assertTrue(expectedCommand.contains("--javascript-delay"), "Should contain javascript-delay");
        assertTrue(expectedCommand.contains("1000"), "Should contain 1000ms delay");
        assertTrue(expectedCommand.contains("--quiet"), "Should contain quiet parameter");
        assertTrue(expectedCommand.contains("-"), "Should contain stdin input parameter");
        assertTrue(expectedCommand.contains(outputPath), "Should contain output path");
    }

    /**
     * Generate PDF using the same parameters as buildWkhtmltopdfCommand method
     */
    private String generatePdfWithSameParameters(String htmlContent) throws Exception {
        // Generate unique filename
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        AtomicLong counter = new AtomicLong(System.currentTimeMillis() % 1000);
        String pdfFileName = "test_" + timestamp + "_" + counter.incrementAndGet() + ".pdf";

        Path outputPath = tempDir.resolve(pdfFileName);

        // Build command with same parameters as buildWkhtmltopdfCommand
        List<String> command = buildWkhtmltopdfCommandForTest(outputPath.toString());

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // Write HTML content to process stdin
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(process.getOutputStream(), StandardCharsets.UTF_8))) {
            writer.write(htmlContent);
            writer.flush();
        }

        // Wait for process completion with timeout
        boolean finished = process.waitFor(30, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("PDF generation timeout");
        }

        int exitCode = process.exitValue();

        if (exitCode != 0) {
            throw new RuntimeException("wkhtmltopdf process exited with code: " + exitCode);
        }

        return pdfFileName;
    }

    /**
     * Build wkhtmltopdf command with the same parameters as the service method This mirrors the buildWkhtmltopdfCommand method exactly
     */
    private List<String> buildWkhtmltopdfCommandForTest(String outputPath) {
        List<String> command = new ArrayList<>();
        command.add("wkhtmltopdf");

        // Page settings
        command.add("--page-size");
        command.add("A4");

        // Margins
        command.add("--margin-top");
        command.add("0.75in");
        command.add("--margin-right");
        command.add("0.75in");
        command.add("--margin-bottom");
        command.add("0.75in");
        command.add("--margin-left");
        command.add("0.75in");

        // Encoding and rendering options
        command.add("--encoding");
        command.add("UTF-8");
        command.add("--no-outline");
        command.add("--disable-smart-shrinking");
        command.add("--print-media-type");

        // Error handling
        command.add("--load-error-handling");
        command.add("ignore");
        command.add("--load-media-error-handling");
        command.add("ignore");

        // JavaScript delay
        command.add("--javascript-delay");
        command.add("1000");

        // Quiet mode for better performance
        command.add("--quiet");

        // Input from stdin
        command.add("-");

        // Output file
        command.add(outputPath);

        return command;
    }

    /**
     * Create test HTML content for PDF generation
     */
    private String createTestHtmlContent() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>Test PDF Document</title>\n" +
                "    <style>\n" +
                "        body { \n" +
                "            font-family: Arial, sans-serif; \n" +
                "            margin: 40px; \n" +
                "            line-height: 1.6;\n" +
                "        }\n" +
                "        h1 { \n" +
                "            color: #2c3e50; \n" +
                "            border-bottom: 2px solid #3498db;\n" +
                "            padding-bottom: 10px;\n" +
                "        }\n" +
                "        .info-box {\n" +
                "            background-color: #f8f9fa;\n" +
                "            border: 1px solid #dee2e6;\n" +
                "            border-radius: 5px;\n" +
                "            padding: 20px;\n" +
                "            margin: 20px 0;\n" +
                "        }\n" +
                "        .test-params {\n" +
                "            background-color: #e8f5e8;\n" +
                "            padding: 15px;\n" +
                "            border-left: 4px solid #28a745;\n" +
                "            margin: 15px 0;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>PDF Generation Unit Test</h1>\n" +
                "    <div class=\"info-box\">\n" +
                "        <p><strong>Test Purpose:</strong> Verify HTML to PDF conversion</p>\n" +
                "        <p><strong>Technology:</strong> wkhtmltopdf with Java Spring Boot</p>\n" +
                "        <p><strong>Generated:</strong> " + LocalDateTime.now() + "</p>\n" +
                "    </div>\n" +
                "    \n" +
                "    <div class=\"test-params\">\n" +
                "        <h3>wkhtmltopdf Parameters Used:</h3>\n" +
                "        <ul>\n" +
                "            <li>Page Size: A4</li>\n" +
                "            <li>Margins: 0.75in (all sides)</li>\n" +
                "            <li>Encoding: UTF-8</li>\n" +
                "            <li>No outline, disable smart shrinking</li>\n" +
                "            <li>Print media type</li>\n" +
                "            <li>JavaScript delay: 1000ms</li>\n" +
                "            <li>Error handling: ignore</li>\n" +
                "            <li>Input: stdin (-)</li>\n" +
                "            <li>Quiet mode enabled</li>\n" +
                "        </ul>\n" +
                "    </div>\n" +
                "    \n" +
                "    <h2>Test Results</h2>\n" +
                "    <p>If you can see this content in a PDF file, the test has passed successfully!</p>\n" +
                "    \n" +
                "    <h3>Features Tested:</h3>\n" +
                "    <ol>\n" +
                "        <li>HTML parsing and rendering</li>\n" +
                "        <li>CSS styling application</li>\n" +
                "        <li>UTF-8 character encoding</li>\n" +
                "        <li>Page layout and margins</li>\n" +
                "        <li>Multi-threaded processing</li>\n" +
                "        <li>Memory-efficient processing (no disk I/O for HTML)</li>\n" +
                "    </ol>\n" +
                "</body>\n" +
                "</html>";
    }

    @Test
    public void test() {
        try {
            String html = TestSourceLoader.loadConfigFromFile("formtemplate/invoice/invoice-template4.html");
            Document doc = Jsoup.parse(html);
            // distill all the value of style by css selector
            Elements elements = doc.select("[style]");
            Map<String, AtomicInteger> styleCount = new HashMap<>();

            Map<String, Set<Element>> styleElementId = new HashMap<>();

            for (Element element : elements) {
                String styleValue = element.attr("style");

                styleValue = Lists.newArrayList(styleValue.split(";")).stream()
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .sorted()
                        .collect(Collectors.joining(";"));

                styleCount.computeIfAbsent(styleValue, key -> new AtomicInteger(0)).incrementAndGet();
                styleElementId.computeIfAbsent(styleValue, key -> new HashSet(0)).add(element);
            }
            System.out.println(styleCount.size());
            styleCount.forEach((key, val) -> {
                log.info("val::{}, key::{}", val, key);
            });
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    
    @Test
    public void hh() throws IOException, InterruptedException {
        ChromeOptionsConfig config = new ChromeOptionsConfig();
        ChromeOptions chromeOptions = config.chromiumOptions();
        List<String> commands = chromeOptions.toCommand();

        // execute Chrome command
        ProcessBuilder processBuilder = new ProcessBuilder(commands);
        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();

        // read output log
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("Chrome output: {}", line);
            }
        }

        // wait for process finished
        boolean finished = process.waitFor(30000, TimeUnit.MILLISECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("Chrome process timeout after " + 30000 + "ms");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            log.error("Task-{}: Chrome process failed with output: {}", 1, output.toString());
        } else {
            log.info("Task-{}: Chrome process finished with exit code: {}", 1, exitCode);
        }
    }
}
