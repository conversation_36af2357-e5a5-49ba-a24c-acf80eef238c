package io.nue.pdf.service;

import org.junit.Test;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ProcessTest {

    private static final String CHROME_BINARY = "/Users/<USER>/workspace-rc/nue-pdf-main/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium";
    private static final String TEST_URL = "https://app.test.nue.io/view-invoice/BOCNeFOeuvg3rz3HBhV-CkqdZAClRL5E88C73H0FMtjE5fiE7WM7r4j7W1RXX_e7CCLNduw0WvyJU0YBYKst49bIha2aO7pjRX0d7V5e1gGkrlsFYFqd0s2UarfEWLAZ7E-CQbvjh_Bg792yf1n813seAnadShlICFI6U9OEhOMBemIyyz70i94pMiJ_UfDVkZJlm6byvo=?internal=true";

    /**
     * Validate Chrome binary path exists and is executable on macOS
     */
    private void validateChromeBinary(String chromePath) {
        File chromeFile = new File(chromePath);
        if (!chromeFile.exists()) {
            throw new RuntimeException("Chrome binary not found: " + chromePath);
        }
        if (!chromeFile.canExecute()) {
            throw new RuntimeException("Chrome binary not executable: " + chromePath);
        }
        System.out.println("Chrome binary validated: " + chromePath);
    }

    /**
     * Sanitize file path for macOS - handle spaces and special characters
     */
    private String sanitizeFilePath(String filePath) {
        // Ensure parent directory exists
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
            System.out.println("Created directory: " + parentDir.getAbsolutePath());
        }

        // Return absolute path to avoid any relative path issues
        return file.getAbsolutePath();
    }

    /**
     * Validate URL format and handle special characters
     */
    private String validateAndSanitizeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL cannot be null or empty");
        }

        // Basic URL validation
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            throw new IllegalArgumentException("URL must start with http:// or https://");
        }

        // Log URL length and special characters for debugging
        System.out.println("URL length: " + url.length());
        System.out.println("URL contains spaces: " + url.contains(" "));
        System.out.println("URL contains special chars: " + url.matches(".*[&=?%].*"));

        return url;
    }

    /**
     * Build Chrome command with enhanced macOS compatibility
     */
    private List<String> buildChromeCommand() {
        List<String> command = new ArrayList<>();

        // Validate Chrome binary first
        validateChromeBinary(CHROME_BINARY);

        // Chrome executable path - use absolute path
        command.add(CHROME_BINARY);

        // Chrome arguments optimized for macOS
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");

        // Handle PDF output path with spaces
        String pdfPath = sanitizeFilePath("/Users/<USER>/Downloads/magiclink background2.pdf");
        command.add("--print-to-pdf=" + pdfPath);

        // PDF formatting options
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");

        // Timing options for better page loading
        command.add("--virtual-time-budget=20000");

        // Additional macOS-specific stability options
        command.add("--disable-background-timer-throttling");
        command.add("--disable-backgrounding-occluded-windows");
        command.add("--disable-renderer-backgrounding");

        // Validate and add URL (last parameter)
        String validatedUrl = validateAndSanitizeUrl(TEST_URL);
        command.add(validatedUrl);

        return command;
    }

    /**
     * Enhanced method for application use with parameter validation
     */
    private List<String> buildChromeCommandForApp(String chromeBinary, String outputPath, String url) {
        List<String> command = new ArrayList<>();

        // Validate inputs
        validateChromeBinary(chromeBinary);
        String sanitizedPath = sanitizeFilePath(outputPath);
        String validatedUrl = validateAndSanitizeUrl(url);

        // Build command
        command.add(chromeBinary);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf=" + sanitizedPath);
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");

        // macOS stability options
        command.add("--disable-background-timer-throttling");
        command.add("--disable-backgrounding-occluded-windows");
        command.add("--disable-renderer-backgrounding");

        command.add(validatedUrl);

        return command;
    }

    @Test
    public void testChromeCommandExecution() throws Exception {
        System.out.println("Testing Chrome command execution...");

        // Build command
        List<String> command = buildChromeCommand();

        System.out.println("Command built with " + command.size() + " arguments:");
        for (int i = 0; i < command.size(); i++) {
            System.out.println("  [" + i + "] " + command.get(i));
        }

        // Create ProcessBuilder
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        System.out.println("\nExecuting Chrome process...");
        Process process = processBuilder.start();

        // Read output
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                System.out.println("Chrome output: " + line);
            }
        }

        // Wait for process completion
        boolean finished = process.waitFor(30, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            System.out.println("Process timed out after 30 seconds");
            throw new RuntimeException("Chrome process timeout");
        }

        int exitCode = process.exitValue();
        System.out.println("Process finished with exit code: " + exitCode);

        // Check for Multiple targets error
        String outputStr = output.toString();
        if (outputStr.contains("Multiple targets are not supported")) {
            System.out.println("ERROR: Multiple targets error detected!");
            throw new RuntimeException("Multiple targets error occurred");
        }

        // Check if PDF was created
        java.io.File pdfFile = new java.io.File("/Users/<USER>/Downloads/magiclink background2.pdf");
        if (pdfFile.exists() && pdfFile.length() > 0) {
            System.out.println("SUCCESS: PDF file created - " + pdfFile.length() + " bytes");
        } else {
            System.out.println("WARNING: PDF file not created or empty");
        }

        // Assert success
        if (exitCode != 0) {
            throw new RuntimeException("Chrome process failed with exit code: " + exitCode + "\nOutput: " + outputStr);
        }

        System.out.println("Test completed successfully!");
    }
}
