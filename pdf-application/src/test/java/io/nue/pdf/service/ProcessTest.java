package io.nue.pdf.service;

import org.junit.Test;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ProcessTest {

    private static final String CHROME_BINARY = "/Users/<USER>/workspace-rc/nue-pdf-main/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium";
    private static final String TEST_URL = "https://app.test.nue.io/view-invoice/BOCNeFOeuvg3rz3HBhV-CkqdZAClRL5E88C73H0FMtjE5fiE7WM7r4j7W1RXX_e7CCLNduw0WvyJU0YBYKst49bIha2aO7pjRX0d7V5e1gGkrlsFYFqd0s2UarfEWLAZ7E-CQbvjh_Bg792yf1n813seAnadShlICFI6U9OEhOMBemIyyz70i94pMiJ_UfDVkZJlm6byvo=?internal=true";

    /**
     * Build Chrome command based on your working command line
     */
    private List<String> buildChromeCommand() {
        List<String> command = new ArrayList<>();

        // Chrome executable path
        command.add(CHROME_BINARY);

        // Chrome arguments (exactly matching your working command)
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--no-sandbox");
        command.add("--disable-setuid-sandbox");
        command.add("--print-to-pdf=/Users/<USER>/Downloads/magiclink background2.pdf");
        command.add("--print-to-pdf-no-header");
        command.add("--print-to-pdf-landscape");
        command.add("--virtual-time-budget=20000");

        // URL (last parameter)
        command.add(TEST_URL);

        return command;
    }

    @Test
    public void testChromeCommandExecution() throws Exception {
        System.out.println("Testing Chrome command execution...");

        // Build command
        List<String> command = buildChromeCommand();

        System.out.println("Command built with " + command.size() + " arguments:");
        for (int i = 0; i < command.size(); i++) {
            System.out.println("  [" + i + "] " + command.get(i));
        }

        // Create ProcessBuilder
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        System.out.println("\nExecuting Chrome process...");
        Process process = processBuilder.start();

        // Read output
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                System.out.println("Chrome output: " + line);
            }
        }

        // Wait for process completion
        boolean finished = process.waitFor(30, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            System.out.println("Process timed out after 30 seconds");
            throw new RuntimeException("Chrome process timeout");
        }

        int exitCode = process.exitValue();
        System.out.println("Process finished with exit code: " + exitCode);

        // Check for Multiple targets error
        String outputStr = output.toString();
        if (outputStr.contains("Multiple targets are not supported")) {
            System.out.println("ERROR: Multiple targets error detected!");
            throw new RuntimeException("Multiple targets error occurred");
        }

        // Check if PDF was created
        java.io.File pdfFile = new java.io.File("/Users/<USER>/Downloads/magiclink background2.pdf");
        if (pdfFile.exists() && pdfFile.length() > 0) {
            System.out.println("SUCCESS: PDF file created - " + pdfFile.length() + " bytes");
        } else {
            System.out.println("WARNING: PDF file not created or empty");
        }

        // Assert success
        if (exitCode != 0) {
            throw new RuntimeException("Chrome process failed with exit code: " + exitCode + "\nOutput: " + outputStr);
        }

        System.out.println("Test completed successfully!");
    }
}
