package io.nue.pdf.service;

import io.nue.pdf.common.Constants;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.ElementNotInteractableException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.devtools.Command;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v126.page.Page;
import org.openqa.selenium.devtools.v126.page.Page.PrintToPDFResponse;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Base64;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import java.util.logging.Level;

public class ChromePDFPrinter {

    private static final Logger log = LoggerFactory.getLogger(ChromePDFPrinter.class);

    private static final String download_dir = "/Users/<USER>/Downloads/";

    public static void main(String[] args) {
        ChromePDFPrinter printer = new ChromePDFPrinter();
        ChromeOptions chromiumOptions = printer.chromiumOptions();
        // 1. init chromeDriver headless
        ChromeDriver webDriver = new ChromeDriver(chromiumOptions);

        // 3.
        long startTime = System.currentTimeMillis();
        
        String handler = webDriver.getWindowHandle();
        WebDriverWait webDriverWait = new WebDriverWait(webDriver, Duration.ofSeconds(120));
        webDriverWait.withMessage(new Supplier<String>() {
            @Override
            public String get() {
                return String.format(
                        "Thread %d, Timeout waiting for download button in tab: %s. Current URL: %s",
                        Thread.currentThread().threadId(), handler, webDriver.getCurrentUrl()
                );
            }
        });

        webDriverWait.pollingEvery(Duration.ofMillis(1000)).ignoring(ElementNotInteractableException.class);
        try (DevTools devTools = webDriver.getDevTools()) {
            devTools.createSession(handler);
            devTools.send(Page.enable());
            
            // Wait for page load complete
            devTools.addListener(Page.loadEventFired(), event -> {
                System.out.println("✅ Page load event fired");
            });

            // Wait for DOM content loaded
            devTools.addListener(Page.domContentEventFired(), event -> {
                System.out.println("✅ DOM content loaded");
            });

            webDriver.get(
                    "http://localhost:3000/view-quote/BAf3xD5YIfAzbAZivSbyrRxLDnW7sdwr7ds4QI0PCCAnpcvt884rxd5L4lKd4rKHGZ23Vp1NnaZ4p06oDMTWIlvnFvtjmmZP1WcvTALvfsHOxcz-ImvM0pHcqHMOg6q1Nr4xl6MHAOicYc_rR3pMbcgWISw26H_uc1cjjVxj79rzMLcolrystV24U_K7RsfpqAkvrjsuwY=?internal=true");

            // button to download by backend
            WebElement result = webDriverWait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(Constants.DOWNLOAD_BUTTON_SELECTOR)));
            log.info("Thread {}, Download button found in tab: {}", Thread.currentThread().threadId(), handler);
            String fileName = result.getAttribute("role-name");
            log.info("fileName ::{}", fileName);
            log.info("open page use :{}", System.currentTimeMillis() - startTime);
            Thread.sleep(5000); // Wait 5 seconds
            
            // 4. 配置 PDF 参数
            Command<PrintToPDFResponse> pdfParams = Page.printToPDF(
                    java.util.Optional.of(true),            // landscape - Landscape mode
                    java.util.Optional.of(false),           // displayHeaderFooter - No headers/footers
                    java.util.Optional.of(true),           // printBackground - No background
                    java.util.Optional.of(1.0),             // scale - 100% scale
                    java.util.Optional.of(11.7),            // paperWidth - A4 landscape width
                    java.util.Optional.of(8.27),            // paperHeight - A4 landscape height
                    java.util.Optional.of(0.4),             // marginTop - Default margin
                    java.util.Optional.of(0.4),             // marginBottom - Default margin
                    java.util.Optional.of(0.4),             // marginLeft - Default margin
                    java.util.Optional.of(0.4),             // marginRight - Default margin
                    java.util.Optional.empty(),             // pageRanges - Print all pages
                    java.util.Optional.of(""),              // headerTemplate - Empty header
                    java.util.Optional.of(""),              // footerTemplate - Empty footer
                    java.util.Optional.of(false),           // preferCSSPageSize - Use specified paper size
                    java.util.Optional.empty(),                     // transferMode - Use default
                    java.util.Optional.of(false),           // generateTaggedPDF - Not needed
                    java.util.Optional.of(false)            // generateDocumentOutline - Not needed
            );

            // 5. Generate PDF with enhanced parameters for large files
            PrintToPDFResponse printToPDFResponse = devTools.send(pdfParams);
            String pdfBase64 = printToPDFResponse.getData();

            // Validate PDF data before processing
            if (pdfBase64 == null || pdfBase64.isEmpty()) {
                throw new RuntimeException("PDF data is empty - page may not be fully loaded");
            }

            String outputPath = download_dir + "magiclink-" + LocalDateTime.now() + ".pdf";
            log.info("outputPath: {}", outputPath);
            File outputFile = Paths.get(outputPath).toFile();

            // 6. Decode and save PDF file with validation
            byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
            
            log.info("PDF size is smaller than expected: {} bytes", pdfBytes.length);

            // try (FileOutputStream fos = new FileOutputStream(outputFile)){
            //     fos.write(pdfBytes);

            //     // Validate file was created and has expected size
            //     if (outputFile.exists() && outputFile.length() > 0) {
            //         System.out.println("✅ PDF successfully saved to: " + outputPath);
            //         System.out.println("📄 File size: " + pdfBytes.length + " bytes");

            //         // Additional validation for PDF format
            //         if (pdfBytes.length >= 4) {
            //             String pdfHeader = new String(pdfBytes, 0, 4);
            //             if (pdfHeader.equals("%PDF")) {
            //                 System.out.println("✅ PDF format validated");
            //             } else {
            //                 System.out.println("⚠️ Warning: File may not be valid PDF format");
            //             }
            //         }
            //     } else {
            //         throw new RuntimeException("PDF file was not created or is empty");
            //     }
            //     System.out.println("PDF generation completed successfully!");
            // } catch (IOException e) {
            //     e.printStackTrace();
            //     throw new RuntimeException("Failed to save PDF file", e);
            // }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 7. 关闭浏览器
        webDriver.quit();
    }

    public ChromeOptions chromiumOptions() {
        ChromeOptions options = baseOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary("126.0.");
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions baseOptions() {
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--disable-gpu"); //GPU (Graphics Processing Unit) acceleration may lead to some compatibility issues or test failures
        options.addArguments("--no-sandbox"); //
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--enable-automation"); //
        options.addArguments("--lang=en-US");
        options.addArguments("--window-size=1920,1080");
        options.addArguments("--canvas-2d-layers");
        options.addArguments("--enable-accelerated-2d-canvas");
        options.addArguments("--disable-dev-shm-usage"); // shared memory usage is disabled to prevent OOM errors
        options.addArguments("--start-maximized");
        options.addArguments("--w3c=true");
        options.addArguments("--enable-downloads"); // enable download

        // Enhanced options for Material-UI and React rendering
        options.addArguments("--force-color-profile=srgb"); // Ensure consistent color rendering
        options.addArguments("--disable-features=VizDisplayCompositor"); // Better CSS rendering
        options.addArguments("--run-all-compositor-stages-before-draw"); // Wait for all CSS to render
        options.addArguments("--disable-background-timer-throttling"); // Don't throttle CSS animations
        options.addArguments("--disable-renderer-backgrounding"); // Keep renderer active for CSS
        options.addArguments("--disable-backgrounding-occluded-windows"); // Ensure full rendering
        options.addArguments("--disable-notifications"); // disable notifications
        options.addArguments("--disable-crash-reporter"); // disable extensions
        options.addArguments("--disable-infobars"); // disable infobars
        options.addArguments("--disable-renderer-backgrounding"); // disable extensions
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-client-side-phishing-detection");
        options.addArguments("--disable-oopr-debug-crash-dump");
        options.addArguments("--no-crash-upload");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-low-res-tiling");
        options.addArguments("--silent");

        // chromium log options
//        if (Boolean.TRUE.equals(chromeLogging)) {
        //enable chrome logging
        options.addArguments("--verbose");
        options.addArguments("--log-level=0"); // INFO = 0, WARNING = 1, LOG_ERROR = 2, LOG_FATAL = 3
        // Set logging preferences
        LoggingPreferences logPrefs = new LoggingPreferences();
        logPrefs.enable(LogType.BROWSER, Level.ALL);
        logPrefs.enable(LogType.DRIVER, Level.ALL);
        logPrefs.enable(LogType.PERFORMANCE, Level.ALL);
        options.setCapability("goog:loggingPrefs", logPrefs);
        // Enable Chrome's internal logging
        options.addArguments("--enable-logging");
        options.addArguments("--v=1");  // Verbosity level 1-3
        // Optional: Specify log output file (adjust path as needed)
        String userHome = System.getProperty("user.home");
        options.addArguments("--log-path=" + userHome + "/chrome.log");
//        }
//        // Enable automatic downloads to Downloads directory
//        Map<String, Object> prefs = new HashMap<>();
//        prefs.put("download.prompt_for_download", false);// disable download prompt
//        prefs.put("profile.default_content_settings.popups", 0);
//        prefs.put("download.default_directory", download_dir); // Use Downloads directory
//        prefs.put("plugins.always_open_pdf_externally", true); // Download PDFs instead of viewing
//        log.info("Chromium default downloads folder:{}", download_dir);
//        options.setExperimentalOption("prefs", prefs);

        //options.addArguments("--incognito"); if set to incognito mode ,the download prompt can not be disabled.
        // Headless mode
        options.addArguments("--headless=new");// new implementation of headless mode

        return options;
    }
    
}
