package io.nue.pdf.service;

import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v126.page.Page;
import org.openqa.selenium.devtools.v126.page.Page.PrintToPDFTransferMode;

import java.util.Base64;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

public class ChromePDFPrinter {
    public static void main(String[] args) {
        // 1. init chromeDriver headless
        ChromeDriver driver = new ChromeDriver();
        DevTools devTools = driver.getDevTools();
        devTools.createSession();

        // 2. 启用 Page 域
        devTools.send(Page.enable());

        // 3. 
        driver.get("https://www.example.com");

        // 4. 配置 PDF 参数
        Page.printToPDF(
        java.util.Optional<java.lang.Boolean> landscape, 
        java.util.Optional<java.lang.Boolean> displayHeaderFooter, 
        java.util.Optional<java.lang.Boolean> printBackground, 
        java.util.Optional<java.lang.Number> scale, 
        java.util.Optional<java.lang.Number> paperWidth, 
        java.util.Optional<java.lang.Number> paperHeight, 
        java.util.Optional<java.lang.Number> marginTop, 
        java.util.Optional<java.lang.Number> marginBottom, 
        java.util.Optional<java.lang.Number> marginLeft, 
        java.util.Optional<java.lang.Number> marginRight, 
        java.util.Optional<java.lang.String> pageRanges, 
        java.util.Optional<java.lang.String> headerTemplate, 
        java.util.Optional<java.lang.String> footerTemplate, 
        java.util.Optional<java.lang.Boolean> preferCSSPageSize, 
        java.util.Optional<PrintToPDFTransferMode> transferMode,
        java.util.Optional<java.lang.Boolean> generateTaggedPDF, 
        java.util.Optional<java.lang.Boolean> generateDocumentOutline
        );
        
        // 5. 
        String pdfBase64 = devTools.send(Page.printToPDF(
                Optional.of(pdfParams),
                Optional.of(PrintToPDFTransferMode.RETURNASBASE64)
        );

        // 6. 解码并保存为PDF文件
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
        try {
            Files.write(Paths.get("output.pdf"), pdfBytes);
            System.out.println("PDF 生成成功！");
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 7. 关闭浏览器
        driver.quit();
    }
}
