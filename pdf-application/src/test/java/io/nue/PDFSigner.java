package io.nue;

import org.junit.Ignore;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Calendar;

@Ignore
public class PDFSigner {
    public static void main(String[] args) throws Exception {
        // Load the keystore
        KeyStore keystore = KeyStore.getInstance("PKCS12");
        char[] password = "your_keystore_password".toCharArray();
        keystore.load(new FileInputStream("path/to/your/keystore.p12"), password);

        // Get the private key and certificate
        PrivateKey privateKey = (PrivateKey) keystore.getKey("your_alias", password);
        Certificate[] certificateChain = keystore.getCertificateChain("your_alias");

        // Load the PDF document
       /* 
       PDDocument document = PDDocument.load(new File("path/to/your/input.pdf"));

        // Create a new signature
        PDSignature signature = new PDSignature();
        signature.setFilter(PDSignature.FILTER_ADOBE_PPKLITE);
        signature.setSubFilter(PDSignature.SUBFILTER_ADBE_PKCS7_DETACHED);
        signature.setName("Signer Name");
        signature.setLocation("Signer Location");
        signature.setReason("Reason for signing");
        signature.setSignDate(Calendar.getInstance());

        // Add the signature to the document
        document.addSignature(signature);

        // Create the signature
        ExternalSigningSupport externalSigning = document.saveIncrementalForExternalSigning(new FileOutputStream("path/to/your/output.pdf"));
        byte[] cmsSignature = sign(externalSigning.getContent(), privateKey, certificateChain);
        externalSigning.setSignature(cmsSignature);
        
        document.close();
        */
    }

//    private static byte[] sign(InputStream content, PrivateKey privateKey, Certificate[] certificateChain) throws Exception {
//        CMSSignedDataGenerator gen = new CMSSignedDataGenerator();
//        ContentSigner signer = new JcaContentSignerBuilder("SHA256withRSA").build(privateKey);
//        gen.addSignerInfoGenerator(new JcaSignerInfoGeneratorBuilder(
//                new JcaDigestCalculatorProviderBuilder().build()).build(signer, (X509Certificate) certificateChain[0]));
//        gen.addCertificates(new JcaCertStore(Arrays.asList(certificateChain)));
//        CMSProcessableInputStream msg = new CMSProcessableInputStream(content);
//        CMSSignedData signedData = gen.(msg, false);
//        return signedData.getEncoded();
//    }
}

