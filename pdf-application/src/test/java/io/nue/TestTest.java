package io.nue;

import org.junit.jupiter.api.Test;

import java.util.List;

public class TestTest {
    
    public static void main(String[] args) {
        TestTest test = new TestTest();
        List list = null;
        test.testEmpty(list);
    }
    
    public void testEmpty(List list) {
        if (list.isEmpty()) {
            throw new RuntimeException();
        }
        
        
    }
    
}
