package io.nue.util;

import com.fasterxml.jackson.core.type.TypeReference;
import io.nue.util.json.JsonUtils;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

public class TestSourceLoader {

    public static <T> T loadConfigFromJsonFile(String path, TypeReference<T> typeReference) throws Exception {
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(path)) {
            return JsonUtils.deserialize(in, typeReference);
        }
    }

    public static String loadConfigFromFile(String path) throws Exception {
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(path)) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = in.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }

            return outputStream.toString();
        }
    }

    public static void main(String[] args) {
        
    }

}
