package io.nue.pdf.config;

import static io.swagger.v3.oas.models.security.SecurityScheme.Type.HTTP;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class AppConfiguration {

    @Bean
    public OpenAPI customOpenAPI() {
        OpenAPI bean = new OpenAPI().components(
                new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme().type(HTTP).scheme("bearer").bearerFormat("JWT"))
                        .addSecuritySchemes("basicAuth", new SecurityScheme().type(HTTP).scheme("basic"))
        );
        return bean;
    }

    @Bean(name = "pdfExecutor")
    public ThreadPoolExecutor pdfTaskExecutor(@Value("${pdf-generator.executor.core-pool-size:5}") int corePoolSize) {
        ThreadPoolExecutor t = new ThreadPoolExecutor(
                corePoolSize,
                corePoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(corePoolSize),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "ChromePdf-" + threadNumber.getAndIncrement());
                        t.setDaemon(false);
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        t.allowCoreThreadTimeOut(true);

        return t;
    }
}
