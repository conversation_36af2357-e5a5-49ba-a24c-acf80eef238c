package io.nue.pdf.config;

import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.common.Constants;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class ChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(ChromeOptionsConfig.class);
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;

    @Value("${selenium.browser.headless:false}")
    private Boolean chromeHeadless;

    private static final int MAGICLINK_WAIT_TIME = 15000; // 15 seconds
    
    @Bean("chromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = printPdfOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions printPdfOptions() {
        ChromeOptions options = new ChromeOptions();

        // Headless mode
        options.addArguments("--headless");
        options.addArguments("--disable-gpu"); //GPU (Graphics Processing Unit) acceleration may lead to some compatibility issues or test failures
        options.addArguments("--disable-software-rasterizer"); //
        options.addArguments("--no-sandbox"); //
        options.addArguments("--disable-plugins"); //
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--enable-automation"); //
        options.addArguments("--run-all-compositor-stages-before-draw"); //

        options.addArguments("--virtual-time-budget=" + MAGICLINK_WAIT_TIME);
        //
        options.addArguments("--print-to-pdf");
        options.addArguments("--print-to-pdf-no-header");
        options.addArguments("--print-to-pdf-paper-width=8.27"); // A4 width
        options.addArguments("--print-to-pdf-paper-height=11.69"); // A4 height
        
        options.addArguments("--window-size=1920,1080");
        options.addArguments("--canvas-2d-layers");
        options.addArguments("--enable-accelerated-2d-canvas");
        options.addArguments("--disable-dev-shm-usage"); // shared memory usage is disabled to prevent OOM errors
        options.addArguments("--start-maximized");
        options.addArguments("--w3c=true");

        options.addArguments("--disable-downloads"); // enable download
        options.addArguments("--disable-notifications"); // disable notifications
        options.addArguments("--disable-crash-reporter"); // disable extensions
        options.addArguments("--disable-infobars"); // disable infobars
        options.addArguments("--disable-renderer-backgrounding"); // disable extensions
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-client-side-phishing-detection");
        options.addArguments("--disable-oopr-debug-crash-dump");
        options.addArguments("--no-crash-upload");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-low-res-tiling");
        options.addArguments("--silent");
        options.addArguments("--incognito"); 
        
        return options;
    }

    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }
    
}
