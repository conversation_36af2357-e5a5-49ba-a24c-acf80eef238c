package io.nue.pdf.config;

import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.common.Constants;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class ChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(ChromeOptionsConfig.class);
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;

    @Value("${selenium.browser.headless:false}")
    private Boolean chromeHeadless;

    private static final int MAGICLINK_WAIT_TIME = 15000; // 15 seconds
    
    @Bean("chromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = printPdfOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions printPdfOptions() {
        ChromeOptions options = new ChromeOptions();

        // 🔑 修复Multiple targets错误的关键参数
        options.addArguments("--headless");
        options.addArguments("--single-process");        // 🔑 强制单进程模式
        options.addArguments("--no-zygote");            // 🔑 禁用zygote进程
        options.addArguments("--disable-gpu");          // GPU acceleration may lead to compatibility issues
        options.addArguments("--disable-software-rasterizer");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-setuid-sandbox"); // 额外的沙箱禁用

        // 禁用可能导致多进程的功能
        options.addArguments("--disable-plugins");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-background-networking");
        options.addArguments("--disable-sync");          // 禁用同步功能
        options.addArguments("--disable-translate");     // 禁用翻译功能

        // 进程和渲染优化
        options.addArguments("--run-all-compositor-stages-before-draw");
        options.addArguments("--disable-renderer-backgrounding");
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");

        // magiclink等待时间
        options.addArguments("--virtual-time-budget=" + MAGICLINK_WAIT_TIME);

        // PDF输出配置
        options.addArguments("--print-to-pdf");
        options.addArguments("--print-to-pdf-no-header");
        options.addArguments("--print-to-pdf-paper-width=8.27");  // A4 width
        options.addArguments("--print-to-pdf-paper-height=11.69"); // A4 height

        // 窗口和显示设置
        options.addArguments("--window-size=1920,1080");
        options.addArguments("--force-device-scale-factor=1");
        options.addArguments("--disable-dev-shm-usage");  // 防止OOM错误

        // 网络和安全设置
        options.addArguments("--disable-web-security");
        options.addArguments("--ignore-certificate-errors");
        options.addArguments("--ignore-ssl-errors");
        options.addArguments("--ignore-certificate-errors-spki-list");

        // 禁用不必要的功能以提高性能和稳定性
        options.addArguments("--disable-notifications");
        options.addArguments("--disable-crash-reporter");
        options.addArguments("--disable-client-side-phishing-detection");
        options.addArguments("--disable-oopr-debug-crash-dump");
        options.addArguments("--no-crash-upload");
        options.addArguments("--disable-low-res-tiling");
        options.addArguments("--silent");
        options.addArguments("--incognito");

        // 内存优化
        options.addArguments("--memory-pressure-off");
        options.addArguments("--max_old_space_size=4096");

        // 移除可能导致问题的参数
        // options.addArguments("--remote-allow-origins=*");     // 可能导致多进程
        // options.addArguments("--enable-automation");          // 可能导致多进程
        // options.addArguments("--canvas-2d-layers");           // 可能导致GPU相关问题
        // options.addArguments("--enable-accelerated-2d-canvas"); // 可能导致GPU相关问题
        // options.addArguments("--start-maximized");            // headless模式下不需要
        // options.addArguments("--w3c=true");                   // 可能导致兼容性问题
        // options.addArguments("--disable-downloads");          // 不需要禁用下载
        // options.addArguments("--disable-infobars");           // headless模式下不需要

        log.info("Chrome options configured with single-process mode to fix Multiple targets error");

        return options;
    }

    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }
    
}
