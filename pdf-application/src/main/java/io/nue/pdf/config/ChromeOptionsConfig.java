package io.nue.pdf.config;

import static io.nue.pdf.common.Constants.CHROME_OPTION_PRINT_TO_PDF;

import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class ChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(ChromeOptionsConfig.class);
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;

    @Value("${selenium.browser.headless:false}")
    private Boolean chromeHeadless;

    private static final int MAGICLINK_WAIT_TIME = 15000; // 15 seconds
    
    @Bean("chromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = printPdfOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions printPdfOptions() {
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--headless");
        options.addArguments("--incognito");
        options.addArguments("--disable-gpu");          // GPU acceleration may lead to compatibility issues
        options.addArguments("--no-sandbox");
        options.addArguments("--run-all-compositor-stages-before-draw");
        
        // wait for page render
        options.addArguments("--virtual-time-budget=" + MAGICLINK_WAIT_TIME);

        // PDF output 
        options.addArguments("--print-to-pdf-no-header");
        options.addArguments(CHROME_OPTION_PRINT_TO_PDF);

        // 
//        options.addArguments("--disable-dev-shm-usage");  // 防止OOM错误
//        // disable functions may cause multi-thread
//        options.addArguments("--disable-plugins");
//        options.addArguments("--disable-extensions");
//        options.addArguments("--disable-background-networking");
//        options.addArguments("--disable-sync");          // 
//        options.addArguments("--disable-translate");     // 
//        options.addArguments("--disable-setuid-sandbox"); // 
//
//        // optimize process and render
//        
//        options.addArguments("--disable-renderer-backgrounding");
//        options.addArguments("--disable-background-timer-throttling");
//        options.addArguments("--disable-backgrounding-occluded-windows");
//        
//        // network and security
//        options.addArguments("--disable-web-security");
//        options.addArguments("--ignore-certificate-errors");
//        options.addArguments("--ignore-ssl-errors");
//        options.addArguments("--ignore-certificate-errors-spki-list");
//
//        // Disable unnecessary features to improve performance and stability.
//        options.addArguments("--disable-notifications");
//        options.addArguments("--disable-crash-reporter");
//        options.addArguments("--disable-client-side-phishing-detection");
//        options.addArguments("--disable-oopr-debug-crash-dump");
//        options.addArguments("--no-crash-upload");
//        options.addArguments("--disable-low-res-tiling");
//        options.addArguments("--silent");
//        options.addArguments("--incognito");
//
//        // memory 
//        options.addArguments("--memory-pressure-off");
//        options.addArguments("--max_old_space_size=4096");

        log.info("Chrome options configured with single-process mode to fix Multiple targets error");

        return options;
    }

    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }

}
