package io.nue.pdf.service;

import static io.nue.pdf.common.Constants.CHROME_OPTION_PRINT_TO_PDF;
import static io.nue.pdf.common.Constants.DOWNLOADS_FOLDER;

import com.google.common.collect.Lists;
import io.nue.exception.NueException;
import io.nue.pdf.bean.ChromeOptions;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class PdfGenerationServiceImpl implements PdfGenerationService {

    private static final Logger log = LoggerFactory.getLogger(PdfGenerationServiceImpl.class);

    // Thread pool for PDF generation
    private final ThreadPoolExecutor threadPoolExecutor;
    private final ChromeOptions chromeOptions;

    // Counter for generating unique filenames
    private final AtomicLong fileCounter = new AtomicLong(0);
    private final ConcurrentMap<String, Boolean> urlMap = new ConcurrentHashMap<>();

    // Timeout for PDF generation process (in milli seconds)
    private static final int PDF_GENERATION_TIMEOUT = 30000;

    private static final String DOWNLOAD_BUTTON_SELECTOR = "div#magiclink-downloadpdf-hidden";

    public PdfGenerationServiceImpl(ThreadPoolExecutor threadPoolExecutor, @Qualifier("chromeOptions") ChromeOptions chromeOptions) {
        this.threadPoolExecutor = threadPoolExecutor;
        this.chromeOptions = chromeOptions;
    }

    @Override
    public boolean convertUrlToPdf(String fileName, String magiclinkUrl, ChromeOptions chromeOptions) {
        try {
            if (urlMap.putIfAbsent(magiclinkUrl, true) != null) {
                log.info("Pdf is generating for magiclinkUrl: " + magiclinkUrl);
                throw new NueException("PDF_IS_GENERATING", "Pdf generation is in progressing, please retry later.");
            }
            Future<Boolean> pdfFuture = threadPoolExecutor.submit(
                    () -> {
                        Boolean pdfSuccess = convertUrlToPdf(magiclinkUrl, fileName, fileCounter.incrementAndGet(), chromeOptions);
                        log.info("Pdf generated for magiclinkUrl: {}, fileName: {}", magiclinkUrl, fileName);
                        return pdfSuccess;
                    }
            );

            return pdfFuture.get();
        } catch (InterruptedException e) {
            log.error("InterruptedException while executing task", e);
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            log.error("ExecutionException while executing task", e);
            throw new RuntimeException(e);
        } catch (RejectedExecutionException e) {
            log.error("Task rejected", e);
            throw new NueException("PDF_GENERATION_REJECTED", "Pdf generation task is rejected, please try again later.");
        } finally {
            urlMap.remove(magiclinkUrl);
        }
    }

    private Boolean convertUrlToPdf(String url, String fileName, long taskId, ChromeOptions chromeOptions) throws Exception {
        log.info("Task-{}: Starting PDF conversion for URL: {}", taskId, url);

        try {
            // Chrome command
            List<String> command = buildChromeCommand(url, fileName, taskId, chromeOptions);

            log.info("Task-{}: Executing Chrome command: {}", taskId, String.join(" ", command));

            // execute Chrome command
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            // read output log
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.debug("Task-{}: Chrome output: {}", taskId, line);
                }
            }

            // wait for process finished
            boolean finished = process.waitFor(PDF_GENERATION_TIMEOUT, TimeUnit.MILLISECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("Chrome process timeout after " + PDF_GENERATION_TIMEOUT + "ms");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("Task-{}: Chrome process failed with output: {}", taskId, output.toString());
                return false;
            } else {
                log.info("Task-{}: Chrome process finished with exit code: {}", taskId, exitCode);
                return true;
            }
        } catch (Exception e) {
            log.error("Task-{}: PDF conversion failed for URL: {}", taskId, url, e);
            throw e;
        }
    }

    /**
     *
     */
    private List<String> buildChromeCommand(String url, String fileName, long taskId, ChromeOptions chromeOptions) {
        List<String> command = null;
        if (chromeOptions == null) {
            command = this.chromeOptions.toCommand();
            int pdfDirIdx = command.indexOf(CHROME_OPTION_PRINT_TO_PDF);
            if (pdfDirIdx >= 0) {
                Path path = Paths.get(DOWNLOADS_FOLDER, fileName + ".pdf");
                command.add(pdfDirIdx + 1, path.toString());
            }
        } else {
            chromeOptions.setBinary(this.chromeOptions.getBinary());
            command = chromeOptions.toCommand();
        }

        command.add(StringUtils.wrap(url, "\""));
        log.info("Task-{}: Chrome command built with {} arguments", taskId, command.size());

        return command;
    }

}
