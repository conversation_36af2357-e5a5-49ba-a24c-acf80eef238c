package io.nue.pdf.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class ChromePdfService {
    
    private static final Logger logger = LoggerFactory.getLogger(ChromePdfService.class);

    // Timeout for PDF generation process (in milli seconds)
    private static final int PDF_GENERATION_TIMEOUT = 30000;
    
    // Chrome可执行文件路径
    private static final String[] CHROME_PATHS = {
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "/usr/bin/google-chrome",
        "/usr/bin/google-chrome-stable",
        "/usr/bin/chromium",
        "/opt/google/chrome/chrome",
        "google-chrome",
        "chromium"
    };
    
    // PDF输出目录
    private static final String PDF_OUTPUT_DIR = "/tmp";

    // 线程池配置
    private static final int CORE_POOL_SIZE = 4;
    private static final int MAX_POOL_SIZE = 10;
    private static final int QUEUE_CAPACITY = 100;
    
    private final ThreadPoolExecutor threadPool;
    private final AtomicInteger taskCounter = new AtomicInteger(0);
    private String chromeExecutablePath;
    
    public ChromePdfService() {
        // init ThreadPool 
        this.threadPool = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "ChromePdf-" + threadNumber.getAndIncrement());
                    t.setDaemon(false);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // find Chrome executable program
        this.chromeExecutablePath = findChromeExecutable();
        
        // create PDF output directory.
        createPdfOutputDirectory();
        
        logger.info("ChromePdfService initialized with Chrome path: {}", chromeExecutablePath);
        logger.info("PDF output directory: {}", PDF_OUTPUT_DIR);
        logger.info("Thread pool configuration - Core: {}, Max: {}, Queue: {}", 
                   CORE_POOL_SIZE, MAX_POOL_SIZE, QUEUE_CAPACITY);
    }
    
    /**
     * convert URL to PDF async
     */
    public CompletableFuture<String> convertUrlToPdfAsync(String url) {
        int taskId = taskCounter.incrementAndGet();
        logger.info("Task-{}: Starting async PDF conversion for URL: {}", taskId, url);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return convertUrlToPdf(url, taskId);
            } catch (Exception e) {
                logger.error("Task-{}: Async PDF conversion failed for URL: {}", taskId, url, e);
                throw new RuntimeException("PDF conversion failed", e);
            }
        }, threadPool);
    }
    
    /**
     *  convert URL to PDF sync
     */
    public String convertUrlToPdf(String url) throws Exception {
        int taskId = taskCounter.incrementAndGet();
        return convertUrlToPdf(url, taskId);
    }
    
    /**
     * core PDF convert method
     */
    private String convertUrlToPdf(String url, int taskId) throws Exception {
        logger.info("Task-{}: Starting PDF conversion for URL: {}", taskId, url);
        
        if (chromeExecutablePath == null) {
            throw new RuntimeException("Chrome executable not found");
        }
        
        // generate PDF file name
        String pdfFileName = generatePdfFileName(url, taskId);
        String pdfFilePath = Paths.get(PDF_OUTPUT_DIR, pdfFileName).toString();
        
        try {
            // 构建Chrome命令
            List<String> command = buildChromeCommand(url, pdfFilePath, taskId);
            
            logger.info("Task-{}: Executing Chrome command: {}", taskId, String.join(" ", command));
            
            // 执行Chrome命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            // 读取输出日志
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    logger.debug("Task-{}: Chrome output: {}", taskId, line);
                }
            }
            
            // 等待进程完成
            boolean finished = process.waitFor(PDF_GENERATION_TIMEOUT, TimeUnit.MILLISECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("Chrome process timeout after " + PDF_GENERATION_TIMEOUT + "ms");
            }
            
            int exitCode = process.exitValue();
            logger.info("Task-{}: Chrome process finished with exit code: {}", taskId, exitCode);
            
            if (exitCode != 0) {
                logger.error("Task-{}: Chrome process failed with output: {}", taskId, output.toString());
                throw new RuntimeException("Chrome process failed with exit code: " + exitCode);
            }
            
            // 验证PDF文件是否生成
            File pdfFile = new File(pdfFilePath);
            if (!pdfFile.exists() || pdfFile.length() == 0) {
                throw new RuntimeException("PDF file was not generated or is empty: " + pdfFilePath);
            }
            
            long fileSize = pdfFile.length();
            logger.info("Task-{}: PDF generated successfully: {} (size: {} bytes)", taskId, pdfFilePath, fileSize);
            
            return pdfFilePath;
            
        } catch (Exception e) {
            logger.error("Task-{}: PDF conversion failed for URL: {}", taskId, url, e);
            throw e;
        }
    }
    
    /**
     * 构建Chrome命令
     */
    private List<String> buildChromeCommand(String url, String pdfFilePath, int taskId) {
        List<String> command = new ArrayList<>();
        
        command.add(chromeExecutablePath);
        command.add("--headless");
        command.add("--disable-gpu");
        command.add("--disable-software-rasterizer");
        command.add("--disable-dev-shm-usage");
        command.add("--no-sandbox");
        command.add("--disable-extensions");
        command.add("--disable-plugins");
        command.add("--disable-images"); // 禁用图片加载以提高速度
        command.add("--run-all-compositor-stages-before-draw");
        
        // 等待magiclink元素出现的时间
        command.add("--virtual-time-budget=" + 30);
        
        // PDF输出配置
        command.add("--print-to-pdf=" + pdfFilePath);
        command.add("--print-to-pdf-no-header");
        
        // 页面大小和边距
        command.add("--print-to-pdf-paper-width=8.27"); // A4宽度（英寸）
        command.add("--print-to-pdf-paper-height=11.69"); // A4高度（英寸）
        
        // 添加JavaScript来等待magiclink元素
        String jsCode = String.format(
            "javascript:(function(){" +
            "console.log('Task-%d: Waiting for magiclink element...');" +
            "var checkElement = function(){" +
            "var element = document.querySelector('%s');" +
            "if(element){" +
            "console.log('Task-%d: Magiclink element found!');" +
            "return true;" +
            "}else{" +
            "console.log('Task-%d: Magiclink element not found, retrying...');" +
            "setTimeout(checkElement, 1000);" +
            "return false;" +
            "}" +
            "};" +
            "checkElement();" +
            "})()",
            taskId, "/tmp", taskId, taskId
        );
        
        // URL参数
        command.add(url);
        
        logger.debug("Task-{}: Chrome command built with {} arguments", taskId, command.size());
        
        return command;
    }
    
    private String findChromeExecutable() {
        logger.info("Searching for Chrome executable...");
        
        for (String path : CHROME_PATHS) {
            File chromeFile = new File(path);
            if (chromeFile.exists() && chromeFile.canExecute()) {
                logger.info("Found Chrome executable: {}", path);
                return path;
            }
        }
        
        // 尝试使用which命令查找
        try {
            Process process = new ProcessBuilder("which", "google-chrome").start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String path = reader.readLine();
                if (path != null && !path.trim().isEmpty()) {
                    File chromeFile = new File(path.trim());
                    if (chromeFile.exists() && chromeFile.canExecute()) {
                        logger.info("Found Chrome executable via 'which': {}", path);
                        return path.trim();
                    }
                }
            }
        } catch (IOException e) {
            logger.debug("Failed to find Chrome using 'which' command", e);
        }
        
        logger.error("Chrome executable not found in any of the expected locations");
        return null;
    }
    
    /**
     * 创建PDF输出目录
     */
    private void createPdfOutputDirectory() {
        try {
            Path pdfDir = Paths.get(PDF_OUTPUT_DIR);
            if (!Files.exists(pdfDir)) {
                Files.createDirectories(pdfDir);
                logger.info("Created PDF output directory: {}", PDF_OUTPUT_DIR);
            } else {
                logger.info("PDF output directory already exists: {}", PDF_OUTPUT_DIR);
            }
        } catch (IOException e) {
            logger.error("Failed to create PDF output directory: {}", PDF_OUTPUT_DIR, e);
            throw new RuntimeException("Cannot create PDF output directory", e);
        }
    }
    
    /**
     * 生成PDF文件名
     */
    private String generatePdfFileName(String url, int taskId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String sanitizedUrl = url.replaceAll("[^a-zA-Z0-9.-]", "_");
        if (sanitizedUrl.length() > 50) {
            sanitizedUrl = sanitizedUrl.substring(0, 50);
        }
        return String.format("task_%d_%s_%s.pdf", taskId, timestamp, sanitizedUrl);
    }
    
    /**
     * 获取线程池状态
     */
    public String getThreadPoolStatus() {
        return String.format(
            "ThreadPool Status - Active: %d, Pool Size: %d, Queue Size: %d, Completed: %d",
            threadPool.getActiveCount(),
            threadPool.getPoolSize(),
            threadPool.getQueue().size(),
            threadPool.getCompletedTaskCount()
        );
    }
    
    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("Shutting down ChromePdfService...");
        threadPool.shutdown();
        try {
            if (!threadPool.awaitTermination(60, TimeUnit.SECONDS)) {
                threadPool.shutdownNow();
                logger.warn("Thread pool did not terminate gracefully, forced shutdown");
            }
        } catch (InterruptedException e) {
            threadPool.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("ChromePdfService shutdown completed");
    }
}
