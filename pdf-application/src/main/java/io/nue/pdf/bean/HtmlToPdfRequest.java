package io.nue.pdf.bean;

public class HtmlToPdfRequest {

    private String fileName;
    private String formType;
    private String magiclink;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getMagiclink() {
        return magiclink;
    }

    public void setMagiclink(String magiclink) {
        this.magiclink = magiclink;
    }

    @Override
    public String toString() {
        return "HtmlToPdfRequest{" +
                "fileName='" + fileName + '\'' +
                ", formType='" + formType + '\'' +
                ", magiclink='" + magiclink + '\'' +
                '}';
    }
}
