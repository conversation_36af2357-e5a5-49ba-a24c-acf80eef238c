package io.nue.pdf.bean;

import static io.nue.pdf.common.Constants.CHROME_OPTION_PRINT_TO_PDF;

import com.google.common.collect.Lists;
import io.nue.util.json.JsonUtils;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChromeOptions<T extends ChromeOptions<?>> {

    private static final String BROWSER_VERSION = "browserVersion";

    private String binary;
    private final List<String> args = new ArrayList<>();
    private final Map<String, String> capability = new HashMap<>();

    public T setBinary(File path) {
        if (path == null) {
            throw new IllegalArgumentException(String.format("%s must be set", path));
        }
        binary = path.getPath();
        return (T) this;
    }

    public T setBinary(String path) {
        if (path == null) {
            throw new IllegalArgumentException(String.format("%s must be set", path));
        }
        binary = path;
        return (T) this;
    }

    public String getBinary() {
        return binary;
    }

    public T addArguments(String argument) {
        args.add(argument);
        //addArguments(Arrays.asList(arguments.split("=")));
        return (T) this;
    }

    public T addArguments(List<String> arguments) {
        args.addAll(arguments);
        return (T) this;
    }

    public T setBrowserVersion(String browserVersion) {
        setCapability(BROWSER_VERSION, browserVersion);
        return (T) this;
    }

    private void setCapability(String capabilityName, String capabilityValue) {
        capability.put(capabilityName, capabilityValue);
    }

    @Override
    public String toString() {
        return "ChromeOptions{" +
                "binary='" + binary + '\'' +
                ", args=" + JsonUtils.serialize(args) +
                ", capability=" + JsonUtils.serialize(capability) +
                '}';
    }

    public List<String> toCommand() {
        if (StringUtils.isEmpty(binary)) {
            throw new IllegalArgumentException(String.format("binary in chromeOption must be set: %s", binary));
        }
        List<String> command = Lists.newArrayList(binary);
        command.addAll(args);

        return command;
    }

}
