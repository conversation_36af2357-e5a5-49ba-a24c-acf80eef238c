package io.nue.pdf.cli;

import io.nue.pdf.service.ChromePdfService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * Chrome PDF CLI - 命令行界面
 * 支持单个URL、批量URL和交互式模式
 */
public class ChromePdfCli {
    
    private static final Logger logger = LoggerFactory.getLogger(ChromePdfCli.class);
    private static final ChromePdfService chromePdfService = new ChromePdfService();
    
    public static void main(String[] args) {
        logger.info("Chrome PDF CLI started");
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down Chrome PDF CLI...");
            chromePdfService.shutdown();
        }));
        
        try {
            if (args.length == 0) {
                // 交互式模式
                runInteractiveMode();
            } else if (args.length == 1) {
                // 单个URL模式
                String url = args[0];
                convertSingleUrl(url);
            } else if (args.length > 1) {
                // 批量URL模式
                List<String> urls = new ArrayList<>();
                for (String arg : args) {
                    urls.add(arg);
                }
                convertBatchUrls(urls);
            }
        } catch (Exception e) {
            logger.error("CLI execution failed", e);
            System.err.println("Error: " + e.getMessage());
            System.exit(1);
        } finally {
            chromePdfService.shutdown();
        }
    }
    
    /**
     * 交互式模式
     */
    private static void runInteractiveMode() {
        System.out.println("=================================");
        System.out.println("Chrome PDF Converter CLI");
        System.out.println("=================================");
        System.out.println("Commands:");
        System.out.println("  convert <url>     - Convert single URL to PDF");
        System.out.println("  batch <url1> <url2> ... - Convert multiple URLs to PDF");
        System.out.println("  status            - Show service status");
        System.out.println("  help              - Show this help");
        System.out.println("  exit              - Exit the program");
        System.out.println();
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("chrome-pdf> ");
            String input = scanner.nextLine().trim();
            
            if (input.isEmpty()) {
                continue;
            }
            
            String[] parts = input.split("\\s+");
            String command = parts[0].toLowerCase();
            
            try {
                switch (command) {
                    case "convert":
                        if (parts.length < 2) {
                            System.out.println("Usage: convert <url>");
                        } else {
                            convertSingleUrl(parts[1]);
                        }
                        break;
                        
                    case "batch":
                        if (parts.length < 2) {
                            System.out.println("Usage: batch <url1> <url2> ...");
                        } else {
                            List<String> urls = new ArrayList<>();
                            for (int i = 1; i < parts.length; i++) {
                                urls.add(parts[i]);
                            }
                            convertBatchUrls(urls);
                        }
                        break;
                        
                    case "status":
                        showStatus();
                        break;
                        
                    case "help":
                        showHelp();
                        break;
                        
                    case "exit":
                    case "quit":
                        System.out.println("Goodbye!");
                        return;
                        
                    default:
                        System.out.println("Unknown command: " + command);
                        System.out.println("Type 'help' for available commands");
                        break;
                }
            } catch (Exception e) {
                logger.error("Command execution failed: {}", command, e);
                System.err.println("Error executing command: " + e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 转换单个URL
     */
    private static void convertSingleUrl(String url) throws Exception {
        System.out.println("Converting URL to PDF: " + url);
        System.out.println("Waiting for magiclink element: " + "div#magiclink-downloadpdf-hidden");
        
        long startTime = System.currentTimeMillis();
        
        try {
            String pdfFilePath = chromePdfService.convertUrlToPdf(url);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.out.println("✅ PDF generated successfully!");
            System.out.println("📁 File: " + pdfFilePath);
            System.out.println("⏱️  Duration: " + duration + "ms");
            
            // 显示文件大小
            java.io.File pdfFile = new java.io.File(pdfFilePath);
            if (pdfFile.exists()) {
                long fileSize = pdfFile.length();
                System.out.println("📊 Size: " + formatFileSize(fileSize));
            }
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.err.println("❌ PDF conversion failed!");
            System.err.println("⏱️  Duration: " + duration + "ms");
            System.err.println("💥 Error: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 批量转换URLs
     */
    private static void convertBatchUrls(List<String> urls) throws Exception {
        System.out.println("Converting " + urls.size() + " URLs to PDF...");
        
        long startTime = System.currentTimeMillis();
        List<CompletableFuture<String>> futures = new ArrayList<>();
        
        // 启动所有异步任务
        for (int i = 0; i < urls.size(); i++) {
            String url = urls.get(i);
            System.out.println((i + 1) + ". Starting: " + url);
            
            CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
            futures.add(future);
        }
        
        // 等待所有任务完成
        int completed = 0;
        int failed = 0;
        
        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<String> future = futures.get(i);
            String url = urls.get(i);
            
            try {
                String pdfFilePath = future.get();
                completed++;
                
                java.io.File pdfFile = new java.io.File(pdfFilePath);
                long fileSize = pdfFile.exists() ? pdfFile.length() : 0;
                
                System.out.println("✅ " + (i + 1) + ". Completed: " + url);
                System.out.println("   📁 File: " + pdfFilePath);
                System.out.println("   📊 Size: " + formatFileSize(fileSize));
                
            } catch (ExecutionException e) {
                failed++;
                System.err.println("❌ " + (i + 1) + ". Failed: " + url);
                System.err.println("   💥 Error: " + e.getCause().getMessage());
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println();
        System.out.println("📊 Batch Conversion Summary:");
        System.out.println("   Total: " + urls.size());
        System.out.println("   Completed: " + completed);
        System.out.println("   Failed: " + failed);
        System.out.println("   Duration: " + duration + "ms");
        System.out.println("   Average: " + (duration / urls.size()) + "ms per URL");
    }
    
    /**
     * 显示服务状态
     */
    private static void showStatus() {
        System.out.println("Chrome PDF Service Status:");
        System.out.println("==========================");
        System.out.println(chromePdfService.getThreadPoolStatus());
        System.out.println("Output Directory: /tmp/pdf");
        System.out.println("Magiclink Selector: div#magiclink-downloadpdf-hidden");
        System.out.println("Wait Time: 15 seconds");
        System.out.println("Timeout: 30 seconds");
    }
    
    /**
     * 显示帮助信息
     */
    private static void showHelp() {
        System.out.println("Chrome PDF Converter CLI Help");
        System.out.println("=============================");
        System.out.println();
        System.out.println("Usage:");
        System.out.println("  java -jar chrome-pdf-cli.jar                    # Interactive mode");
        System.out.println("  java -jar chrome-pdf-cli.jar <url>              # Convert single URL");
        System.out.println("  java -jar chrome-pdf-cli.jar <url1> <url2> ...  # Convert multiple URLs");
        System.out.println();
        System.out.println("Interactive Commands:");
        System.out.println("  convert <url>     - Convert single URL to PDF");
        System.out.println("  batch <url1> <url2> ... - Convert multiple URLs to PDF");
        System.out.println("  status            - Show service status");
        System.out.println("  help              - Show this help");
        System.out.println("  exit              - Exit the program");
        System.out.println();
        System.out.println("Features:");
        System.out.println("  ✅ Multi-threaded PDF generation");
        System.out.println("  ✅ Magiclink detection (div#magiclink-downloadpdf-hidden)");
        System.out.println("  ✅ Automatic Chrome detection");
        System.out.println("  ✅ Comprehensive logging");
        System.out.println("  ✅ Error handling and recovery");
        System.out.println("  ✅ PDF output to /tmp/pdf");
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        }
    }
}
