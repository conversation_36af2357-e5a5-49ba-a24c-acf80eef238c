package io.nue.pdf.controller;

import io.nue.pdf.service.ChromePdfService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Chrome PDF Controller - 提供URL转PDF的REST API
 */
@RestController
@RequestMapping("/api/chrome-pdf")
public class ChromePdfController {
    
    private static final Logger logger = LoggerFactory.getLogger(ChromePdfController.class);
    
    @Autowired
    private ChromePdfService chromePdfService;
    
    /**
     * 同步URL转PDF - 直接返回PDF文件
     */
    @PostMapping("/convert-sync")
    public ResponseEntity<Resource> convertUrlToPdfSync(@RequestBody Map<String, String> request) {
        String url = request.get("url");
        
        if (url == null || url.trim().isEmpty()) {
            logger.error("URL parameter is missing or empty");
            return ResponseEntity.badRequest().build();
        }
        
        logger.info("Received sync PDF conversion request for URL: {}", url);
        
        try {
            String pdfFilePath = chromePdfService.convertUrlToPdf(url);
            
            File pdfFile = new File(pdfFilePath);
            if (!pdfFile.exists()) {
                logger.error("Generated PDF file not found: {}", pdfFilePath);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
            
            Resource resource = new FileSystemResource(pdfFile);
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + pdfFile.getName() + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PDF_VALUE);
            
            logger.info("Returning PDF file: {} (size: {} bytes)", pdfFilePath, pdfFile.length());
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(pdfFile.length())
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Sync PDF conversion failed for URL: {}", url, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 异步URL转PDF - 返回任务ID
     */
    @PostMapping("/convert-async")
    public ResponseEntity<Map<String, Object>> convertUrlToPdfAsync(@RequestBody Map<String, String> request) {
        String url = request.get("url");
        
        if (url == null || url.trim().isEmpty()) {
            logger.error("URL parameter is missing or empty");
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "URL parameter is required");
            return ResponseEntity.badRequest().body(errorResponse);
        }
        
        logger.info("Received async PDF conversion request for URL: {}", url);
        
        try {
            CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PDF conversion started");
            response.put("url", url);
            response.put("taskId", future.hashCode());
            response.put("status", "processing");
            
            // 异步处理完成后的回调
            future.whenComplete((pdfFilePath, throwable) -> {
                if (throwable != null) {
                    logger.error("Async PDF conversion failed for URL: {}", url, throwable);
                } else {
                    logger.info("Async PDF conversion completed for URL: {}, file: {}", url, pdfFilePath);
                }
            });
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to start async PDF conversion for URL: {}", url, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to start PDF conversion: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 批量URL转PDF
     */
    @PostMapping("/convert-batch")
    public ResponseEntity<Map<String, Object>> convertBatchUrlsToPdf(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        java.util.List<String> urls = (java.util.List<String>) request.get("urls");
        
        if (urls == null || urls.isEmpty()) {
            logger.error("URLs parameter is missing or empty");
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "URLs parameter is required");
            return ResponseEntity.badRequest().body(errorResponse);
        }
        
        logger.info("Received batch PDF conversion request for {} URLs", urls.size());
        
        try {
            java.util.List<CompletableFuture<String>> futures = new java.util.ArrayList<>();
            java.util.List<Map<String, Object>> tasks = new java.util.ArrayList<>();
            
            for (String url : urls) {
                if (url != null && !url.trim().isEmpty()) {
                    CompletableFuture<String> future = chromePdfService.convertUrlToPdfAsync(url);
                    futures.add(future);
                    
                    Map<String, Object> task = new HashMap<>();
                    task.put("url", url);
                    task.put("taskId", future.hashCode());
                    task.put("status", "processing");
                    tasks.add(task);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Batch PDF conversion started");
            response.put("totalTasks", tasks.size());
            response.put("tasks", tasks);
            
            // call back when it runs over.
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.error("Batch PDF conversion failed", throwable);
                    } else {
                        logger.info("Batch PDF conversion completed for {} URLs", urls.size());
                    }
                });
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to start batch PDF conversion", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to start batch PDF conversion: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * get service status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("service", "ChromePdfService");
            status.put("status", "running");
            status.put("threadPool", chromePdfService.getThreadPoolStatus());
            status.put("timestamp", java.time.LocalDateTime.now().toString());
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            logger.error("Failed to get service status", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("service", "ChromePdfService");
            errorResponse.put("status", "error");
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

}
