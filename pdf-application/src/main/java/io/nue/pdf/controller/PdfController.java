package io.nue.pdf.controller;

import static io.nue.pdf.common.Constants.DOWNLOADS_FOLDER;

import io.nue.exception.NueException;
import io.nue.pdf.bean.ChromeOptions;
import io.nue.pdf.bean.HtmlToPdfRequest;
import io.nue.pdf.service.PdfGenerationService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@RestController
public class PdfController {

    private static final Logger log = LoggerFactory.getLogger(PdfController.class);

    private final PdfGenerationService pdfGenerationService;
    private final String rootUrl;

    public PdfController(PdfGenerationService pdfGenerationService, @Value("${app.root-url}") String rootUrl) {
        this.pdfGenerationService = pdfGenerationService;
        this.rootUrl = rootUrl;
    }

    @RequestMapping(value = "/magiclink-pdf/view-{formType}/**", method = RequestMethod.GET, produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<?> magiclinkPdf(@PathVariable("formType") String formType, @RequestParam(name = "fileName", required = true) String fileName,
            HttpServletRequest request) {
        String magiclink = request.getRequestURI().substring("/magiclink-pdf".length());

        HtmlToPdfRequest htmlToPdfRequest = new HtmlToPdfRequest();
        htmlToPdfRequest.setFileName(fileName);
        htmlToPdfRequest.setMagiclink(this.rootUrl + magiclink + "internal=true");
        htmlToPdfRequest.setFormType(formType);
        boolean pdfSuccess = false;
        try {
            log.info("Magiclink to pdf -> formType:{}, magiclink:{}, fileName:{}", htmlToPdfRequest.getFormType(), htmlToPdfRequest.getMagiclink(),
                    fileName);
            pdfSuccess = pdfGenerationService.convertUrlToPdf(fileName, this.rootUrl + magiclink + "internal=true", null);
        } catch (NueException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
        } catch (Exception e) {
            log.error("Error convert html to generate PDF : ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }

        if (pdfSuccess) {
            return pdfFileToResponse(fileName);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @RequestMapping(value = "/magiclink-pdf/view-{formType}/**", method = RequestMethod.POST, produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<?> magiclinkPdf(@PathVariable("formType") String formType, @RequestParam(name = "fileName") String fileName,
            HttpServletRequest request, @RequestBody(required = false) List<String> chromeOptionList) {
        String magiclink = request.getRequestURI().substring("/magiclink-pdf".length());

        boolean pdfSuccess = false;
        try {
            log.info("Magiclink to pdf -> formType:{}, magiclink:{}, fileName:{}", formType, magiclink, fileName);
            ChromeOptions chromeOptions = null;
            if (!CollectionUtils.isEmpty(chromeOptionList)) {
                chromeOptions = new ChromeOptions();
                chromeOptions.addArguments(chromeOptionList);
            }
            pdfSuccess = pdfGenerationService.convertUrlToPdf(fileName, this.rootUrl + magiclink + "?internal=true", chromeOptions);
        } catch (NueException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
        } catch (Exception e) {
            log.error("Error convert html to generate PDF : ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }

        if (pdfSuccess) {
            return pdfFileToResponse(fileName);
        } else {
            return ResponseEntity.notFound().build();
        }
    }


    public ResponseEntity<byte[]> pdfFileToResponse(String pdfFileName) {
        Path path = Paths.get(DOWNLOADS_FOLDER, pdfFileName + ".pdf");
        File pdfFile = path.toFile();

        if (!pdfFile.exists() || pdfFile.length() == 0) {
            throw new RuntimeException("PDF file was not generated or is empty: " + pdfFile);
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentLength(pdfFile.length());
            headers.setContentDispositionFormData("attachment", pdfFileName);
            log.info("pdfFileToResponse -> pdfFileName:{}", pdfFileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(pdfFile.length())
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(Files.readAllBytes(path));
        } catch (IOException e) {
            log.error("Error while reading the file: " + pdfFileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } finally {
            // Delete the file in a finally block to ensure it's always executed
            if (pdfFile != null && pdfFile.exists()) {
                boolean deleted = pdfFile.delete();
                if (!deleted) {
                    // Log that the file couldn't be deleted
                    log.info("Failed to delete the file: " + pdfFile.getAbsolutePath());
                }
            }
        }
    }

}
